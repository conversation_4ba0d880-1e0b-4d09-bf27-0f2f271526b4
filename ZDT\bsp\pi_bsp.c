#include "pi_bsp.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 最大可检测矩形数量
#define MAX_RECTANGLES 10

/**
 * @brief �������ڽ��յ��ľ�������
 * @param buffer ���յ������ݻ�����
 * @param length ���ݳ���
 * @param rect_data ������ľ������ݽṹ��ָ��
 * @return 1:�����ɹ� 0:����ʧ��
 * 
 * Э���ʽ: "RECT,count,x1,y1,t1,x2,y2,t2,...,xn,yn,tn"
 * ����: "RECT,2,150,100,1.5,200,150,0.8"
 */
uint8_t ParseRectangleData(char* buffer, uint16_t length, RectangleData_t* rect_data)
{
    char* token;
    char* rest = buffer;
    
    // ��սṹ������
    memset(rect_data, 0, sizeof(RectangleData_t));
    
    // �������ͷ�Ƿ�Ϊ"RECT"
    token = strtok_r(rest, ",", &rest);
    if (token == NULL || strcmp(token, "RECT") != 0) {
        return 0; // ����ͷ��ƥ��
    }
    
    // ��ȡ��������
    token = strtok_r(rest, ",", &rest);
    if (token == NULL) {
        return 0; // �޷���ȡ��������
    }
    
    rect_data->count = atoi(token);
    if (rect_data->count > MAX_RECTANGLES) {
        rect_data->count = MAX_RECTANGLES; // �����������
    }
    
    // 解析每个矩形的数据
    {
        uint8_t i; // 声明循环变量
        for (i = 0; i < rect_data->count; i++) {
        // ����X����
        token = strtok_r(rest, ",", &rest);
        if (token == NULL) return 0;
        rect_data->rects[i].x = atoi(token);
        
        // ����Y����
        token = strtok_r(rest, ",", &rest);
        if (token == NULL) return 0;
        rect_data->rects[i].y = atoi(token);
        
        // ��������ʱ��
        token = strtok_r(rest, ",", &rest);
        if (token == NULL) return 0;
        rect_data->rects[i].exist_time = atof(token);
        }
    }

    return 1; // 解析成功
}

// ʹ��ʾ��
/*
void ProcessSerialData(void)
{
    // �������Ǵ�UART���յ�������
    char rxBuffer[100] = "RECT,2,150,100,1.50,200,150,0.80";
    RectangleData_t rectData;
    
    if (ParseRectangleData(rxBuffer, strlen(rxBuffer), &rectData)) {
        // �����ɹ���������������
        printf("��⵽ %d ������\r\n", rectData.count);
        
        for (uint8_t i = 0; i < rectData.count; i++) {
            printf("���� %d: ����(%d,%d) ����ʱ��:%.2fs\r\n", 
                   i+1, 
                   rectData.rects[i].x, 
                   rectData.rects[i].y, 
                   rectData.rects[i].exist_time);
            
            // ����������ӶԾ������ݵĴ����߼�
            // ���磺����������Ƶ����LED��
        }
    } else {
        printf("���ݽ���ʧ��\r\n");
    }
}
*/

// 全局变量存储最新的视觉数据
RectangleData_t latest_vision_data = {0};
uint8_t vision_data_updated = 0;

// 实时数据统计
static uint32_t total_packets_received = 0;
static uint32_t valid_packets_parsed = 0;
static uint32_t last_packet_time = 0;

// 树莓派处理函数 - 实时数据处理
void pi_proc(void)
{
    char buffer[256];
    rt_size_t len;
    static char line_buffer[512] = {0}; // 行缓冲区，用于处理不完整的数据包
    static uint16_t line_pos = 0;

    // 持续处理环形缓冲区中的所有可用数据
    while((len = rt_ringbuffer_get(&ringbuffer_pi, (uint8_t*)buffer, sizeof(buffer)-1)) > 0) {
        buffer[len] = '\0'; // 添加字符串结束符

        // 调试：输出从环形缓冲区读取的数据
        // my_printf(&huart1, "RingBuf[%d]: %s\r\n", len, buffer);

        // 将新数据追加到行缓冲区
        uint16_t i;
        for(i = 0; i < len && line_pos < sizeof(line_buffer)-1; i++) {
            if(buffer[i] == '\n' || buffer[i] == '\r') {
                // 遇到换行符，处理完整的一行数据
                if(line_pos > 0) {
                    line_buffer[line_pos] = '\0';

                    // 解析视觉数据
                    total_packets_received++; // 统计接收到的数据包
                    if(ParseRectangleData(line_buffer, line_pos, &latest_vision_data)) {
                        vision_data_updated = 1; // 标记数据已更新
                        valid_packets_parsed++; // 统计有效解析的数据包
                        last_packet_time = HAL_GetTick(); // 记录最后接收时间
                        // 可选：添加调试输出
                        // my_printf(&huart1, "Vision: count=%d\r\n", latest_vision_data.count);
                    }

                    line_pos = 0; // 重置行缓冲区
                }
            } else {
                // 普通字符，添加到行缓冲区
                line_buffer[line_pos++] = buffer[i];
            }
        }

        // 防止缓冲区溢出
        if(line_pos >= sizeof(line_buffer)-1) {
            line_pos = 0; // 重置缓冲区
        }
    }
}

// 获取最新视觉数据 - 实时版本
uint8_t GetLatestVisionData(RectangleData_t* data)
{
    if(vision_data_updated && data != NULL) {
        memcpy(data, &latest_vision_data, sizeof(RectangleData_t));
        vision_data_updated = 0; // 清除更新标志
        return 1;
    }
    return 0;
}

// 获取数据接收统计信息
void GetVisionDataStats(uint32_t* total_packets, uint32_t* valid_packets, uint32_t* last_time)
{
    if(total_packets) *total_packets = total_packets_received;
    if(valid_packets) *valid_packets = valid_packets_parsed;
    if(last_time) *last_time = last_packet_time;
}

// 检查数据是否实时（最近1秒内有数据）
uint8_t IsVisionDataRealtime(void)
{
    uint32_t current_time = HAL_GetTick();
    return (current_time - last_packet_time) < 1000 ? 1 : 0;
}

// 手动注入测试数据（用于调试）
void InjectTestVisionData(void)
{
    char test_data[] = "RECT,2,150,100,1.5,200,150,0.8\r\n";
    uint16_t len = strlen(test_data);

    // 直接调用解析函数测试
    if(ParseRectangleData(test_data, len, &latest_vision_data)) {
        vision_data_updated = 1;
        valid_packets_parsed++;
        last_packet_time = HAL_GetTick();
        total_packets_received++;
    }
}

// 检查是否有新的视觉数据（不清除标志）
uint8_t HasNewVisionData(void)
{
    return vision_data_updated;
}

// 获取当前视觉数据（不清除标志，用于实时监控）
uint8_t GetCurrentVisionData(RectangleData_t* data)
{
    if(data != NULL) {
        memcpy(data, &latest_vision_data, sizeof(RectangleData_t));
        return (latest_vision_data.count > 0) ? 1 : 0;
    }
    return 0;
}


