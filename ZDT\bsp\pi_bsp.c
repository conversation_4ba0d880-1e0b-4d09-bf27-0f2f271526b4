#include "pi_bsp.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 最大可检测矩形数量
#define MAX_RECTANGLES 10

/**
 * @brief �������ڽ��յ��ľ�������
 * @param buffer ���յ������ݻ�����
 * @param length ���ݳ���
 * @param rect_data ������ľ������ݽṹ��ָ��
 * @return 1:�����ɹ� 0:����ʧ��
 * 
 * Э���ʽ: "RECT,count,x1,y1,t1,x2,y2,t2,...,xn,yn,tn"
 * ����: "RECT,2,150,100,1.5,200,150,0.8"
 */
uint8_t ParseRectangleData(char* buffer, uint16_t length, RectangleData_t* rect_data)
{
    char* token;
    char* rest = buffer;
    
    // ��սṹ������
    memset(rect_data, 0, sizeof(RectangleData_t));
    
    // �������ͷ�Ƿ�Ϊ"RECT"
    token = strtok_r(rest, ",", &rest);
    if (token == NULL || strcmp(token, "RECT") != 0) {
        return 0; // ����ͷ��ƥ��
    }
    
    // ��ȡ��������
    token = strtok_r(rest, ",", &rest);
    if (token == NULL) {
        return 0; // �޷���ȡ��������
    }
    
    rect_data->count = atoi(token);
    if (rect_data->count > MAX_RECTANGLES) {
        rect_data->count = MAX_RECTANGLES; // �����������
    }
    
    // 解析每个矩形的数据
    {
        uint8_t i; // 声明循环变量
        for (i = 0; i < rect_data->count; i++) {
        // ����X����
        token = strtok_r(rest, ",", &rest);
        if (token == NULL) return 0;
        rect_data->rects[i].x = atoi(token);
        
        // ����Y����
        token = strtok_r(rest, ",", &rest);
        if (token == NULL) return 0;
        rect_data->rects[i].y = atoi(token);
        
        // ��������ʱ��
        token = strtok_r(rest, ",", &rest);
        if (token == NULL) return 0;
        rect_data->rects[i].exist_time = atof(token);
        }
    }

    return 1; // 解析成功
}

// ʹ��ʾ��
/*
void ProcessSerialData(void)
{
    // �������Ǵ�UART���յ�������
    char rxBuffer[100] = "RECT,2,150,100,1.50,200,150,0.80";
    RectangleData_t rectData;
    
    if (ParseRectangleData(rxBuffer, strlen(rxBuffer), &rectData)) {
        // �����ɹ���������������
        printf("��⵽ %d ������\r\n", rectData.count);
        
        for (uint8_t i = 0; i < rectData.count; i++) {
            printf("���� %d: ����(%d,%d) ����ʱ��:%.2fs\r\n", 
                   i+1, 
                   rectData.rects[i].x, 
                   rectData.rects[i].y, 
                   rectData.rects[i].exist_time);
            
            // ����������ӶԾ������ݵĴ����߼�
            // ���磺����������Ƶ����LED��
        }
    } else {
        printf("���ݽ���ʧ��\r\n");
    }
}
*/

// 全局变量存储最新的视觉数据
static RectangleData_t latest_vision_data = {0};
static uint8_t vision_data_updated = 0;

// 树莓派处理函数
void pi_proc(void)
{
    char buffer[128];
    rt_size_t len;

    // 从环形缓冲区读取数据
    len = rt_ringbuffer_get(&ringbuffer_pi, (uint8_t*)buffer, sizeof(buffer)-1);

    if(len > 0) {
        buffer[len] = '\0'; // 添加字符串结束符

        // 解析视觉数据
        if(ParseRectangleData(buffer, len, &latest_vision_data)) {
            vision_data_updated = 1; // 标记数据已更新
        }
    }
}

// 获取最新视觉数据
uint8_t GetLatestVisionData(RectangleData_t* data)
{
    if(vision_data_updated && data != NULL) {
        memcpy(data, &latest_vision_data, sizeof(RectangleData_t));
        vision_data_updated = 0; // 清除更新标志
        return 1;
    }
    return 0;
}


