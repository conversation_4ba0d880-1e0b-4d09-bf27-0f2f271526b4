#include "schedule.h"

typedef struct {
	void (*task_func)(void);
	uint32_t rate_ms;
	uint32_t last_run;
}schedule_task_t;

uint8_t task_num;

static schedule_task_t schedule_task[] = {
	{oled_proc,50,0}, // OLED显示任务，每50ms执行一次，提高刷新率
	{uart_proc,1,0},
	{pi_proc,5,0}, // 视觉数据处理任务，每5ms执行一次，确保实时性

//	{motor_proc,20,0},
//	{encoder_proc,20,0},
	{key_proc,10,0},
//	{gray_proc,20,0}

};

/**
 * @brief ��������ʼ������
 * �������������Ԫ�ظ�������������洢�� task_num ��
 */
void schedule_init(void)
{
	task_num = sizeof(schedule_task) / sizeof(schedule_task_t);
}

/**
 * @brief ���������к���
 * �����������飬����Ƿ���������Ҫִ�С������ǰʱ���Ѿ����������ִ�����ڣ���ִ�и����񲢸����ϴ�����ʱ��
 */
void schedule_run(void)
{
    uint8_t i; // 声明循环变量
    // 遍历任务数组中的所有任务
    for (i = 0; i < task_num; i++)
    {
        // 获取当前的系统时间（毫秒）
        uint32_t now_time = HAL_GetTick();

        // 检查当前时间是否达到任务执行时间
        if (now_time >= schedule_task[i].rate_ms + schedule_task[i].last_run)
        {
            // 更新任务上次运行时间为当前时间
            schedule_task[i].last_run = now_time;

            // 执行任务函数
            schedule_task[i].task_func();
        }
    }
}
