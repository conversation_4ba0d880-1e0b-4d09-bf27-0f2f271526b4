Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(.text) refers to assert.o(.text) for __aeabi_assert
    main.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_OscConfig
    main.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_Init
    main.o(.text) refers to gpio.o(.text) for MX_GPIO_Init
    main.o(.text) refers to dma.o(.text) for MX_DMA_Init
    main.o(.text) refers to usart.o(.text) for MX_USART2_UART_Init
    main.o(.text) refers to tim.o(.text) for MX_TIM1_Init
    main.o(.text) refers to i2c.o(.text) for MX_I2C3_Init
    main.o(.text) refers to schedule.o(.text) for schedule_init
    main.o(.text) refers to mypid.o(.text) for PID_INIT
    main.o(.text) refers to ringbuffer.o(.text) for rt_ringbuffer_init
    main.o(.text) refers to step_motor_bsp.o(.text) for Step_Motor_Init
    main.o(.text) refers to emm_v5.o(.text) for Emm_V5_Reset_CurPos_To_Zero
    main.o(.text) refers to uart_bsp.o(.text) for save_initial_position
    main.o(.text) refers to oled_app.o(.text) for Oled_Init
    main.o(.text) refers to stm32f4xx_hal_uart.o(.text) for HAL_UARTEx_ReceiveToIdle_DMA
    main.o(.text) refers to uart_bsp.o(.bss) for ringbuffer_pool_y
    main.o(.text) refers to usart.o(.bss) for huart3
    dma.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_NVIC_SetPriority
    i2c.o(.text) refers to stm32f4xx_hal_i2c.o(.text) for HAL_I2C_Init
    i2c.o(.text) refers to main.o(.text) for Error_Handler
    i2c.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    i2c.o(.text) refers to i2c.o(.bss) for hi2c3
    tim.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    tim.o(.text) refers to stm32f4xx_hal_tim.o(.text) for HAL_TIM_Base_Init
    tim.o(.text) refers to main.o(.text) for Error_Handler
    tim.o(.text) refers to stm32f4xx_hal_tim_ex.o(.text) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text) refers to tim.o(.bss) for htim1
    usart.o(.text) refers to assert.o(.text) for __aeabi_assert
    usart.o(.text) refers to ringbuffer.o(.text) for rt_ringbuffer_put
    usart.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(.text) refers to stm32f4xx_hal_uart.o(.text) for HAL_UART_Init
    usart.o(.text) refers to main.o(.text) for Error_Handler
    usart.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    usart.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Init
    usart.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_NVIC_SetPriority
    usart.o(.text) refers to usart.o(.bss) for motor_x_buf
    usart.o(.text) refers to uart_bsp.o(.bss) for ringbuffer_x
    stm32f4xx_it.o(.text) refers to assert.o(.text) for __aeabi_assert
    stm32f4xx_it.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_IncTick
    stm32f4xx_it.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.text) refers to stm32f4xx_hal_uart.o(.text) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.text) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f4xx_hal_i2c.o(.text) refers to i2c.o(.text) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal_gpio.o(.text) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_flash.o(.text) refers to stm32f4xx_hal_flash_ex.o(.text) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(.text) refers to stm32f4xx_hal_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.text) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_dma.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_pwr_ex.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_cortex.o(.text) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal_msp.o(.text) for HAL_MspInit
    stm32f4xx_hal.o(.text) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_tim.o(.text) refers to tim.o(.text) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text) refers to stm32f4xx_hal_tim_ex.o(.text) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim_ex.o(.text) refers to stm32f4xx_hal_tim.o(.text) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.text) refers to stm32f4xx_hal_rcc.o(.text) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.text) refers to usart.o(.text) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    stm32f4xx_hal_uart.o(.text) refers to stm32f4xx_hal_dma.o(.text) for HAL_DMA_Start_IT
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    ringbuffer.o(.text) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    emm_v5.o(.text) refers to assert.o(.text) for __aeabi_assert
    emm_v5.o(.text) refers to stm32f4xx_hal_uart.o(.text) for HAL_UART_Transmit
    emm_v5.o(.text) refers to uart_bsp.o(.text) for my_printf
    emm_v5.o(.text) refers to usart.o(.bss) for huart1
    emm_v5.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    encoder_drv.o(.text) refers to assert.o(.text) for __aeabi_assert
    encoder_drv.o(.text) refers to stm32f4xx_hal_tim.o(.text) for HAL_TIM_Encoder_Start
    mypid.o(.text) refers to assert.o(.text) for __aeabi_assert
    mypid.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    mypid.o(.text) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    mypid.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    mypid.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    mypid.o(.text) refers to mypid.o(.bss) for pid_x
    mypid.o(.text) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    mypid.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled_driver.o(.text) refers to assert.o(.text) for __aeabi_assert
    oled_driver.o(.text) refers to vsnprintf.o(.text) for vsnprintf
    oled_driver.o(.text) refers to oled.o(.text) for OLED_ShowString
    oled_driver.o(.text) refers to stm32f4xx_hal_i2c.o(.text) for HAL_I2C_Mem_Write
    oled_driver.o(.text) refers to i2c.o(.bss) for hi2c3
    encoder_bsp.o(.text) refers to assert.o(.text) for __aeabi_assert
    encoder_bsp.o(.text) refers to encoder_drv.o(.text) for Encoder_Driver_Init
    encoder_bsp.o(.text) refers to tim.o(.bss) for htim3
    encoder_bsp.o(.text) refers to encoder_bsp.o(.bss) for left_encoder
    pi_bsp.o(.text) refers to assert.o(.text) for __aeabi_assert
    pi_bsp.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    pi_bsp.o(.text) refers to strtok_r.o(.text) for strtok_r
    pi_bsp.o(.text) refers to strcmpv7m.o(.text) for strcmp
    pi_bsp.o(.text) refers to atoi.o(.text) for atoi
    pi_bsp.o(.text) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    pi_bsp.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    pi_bsp.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    pi_bsp.o(.text) refers to ringbuffer.o(.text) for rt_ringbuffer_get
    pi_bsp.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    pi_bsp.o(.text) refers to pi_bsp.o(.data) for line_pos
    pi_bsp.o(.text) refers to pi_bsp.o(.bss) for line_buffer
    pi_bsp.o(.text) refers to uart_bsp.o(.bss) for ringbuffer_pi
    schedule.o(.text) refers to assert.o(.text) for __aeabi_assert
    schedule.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_GetTick
    schedule.o(.text) refers to schedule.o(.data) for task_num
    schedule.o(.data) refers to oled_app.o(.text) for oled_proc
    schedule.o(.data) refers to uart_bsp.o(.text) for uart_proc
    schedule.o(.data) refers to pi_bsp.o(.text) for pi_proc
    schedule.o(.data) refers to key_bsp.o(.text) for key_proc
    step_motor_bsp.o(.text) refers to assert.o(.text) for __aeabi_assert
    step_motor_bsp.o(.text) refers to emm_v5.o(.text) for Emm_V5_Stop_Now
    step_motor_bsp.o(.text) refers to uart_bsp.o(.text) for my_printf
    step_motor_bsp.o(.text) refers to usart.o(.bss) for huart2
    uart_bsp.o(.text) refers to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text) refers to assert.o(.text) for __aeabi_assert
    uart_bsp.o(.text) refers to vsnprintf.o(.text) for vsnprintf
    uart_bsp.o(.text) refers to stm32f4xx_hal_uart.o(.text) for HAL_UART_Transmit
    uart_bsp.o(.text) refers to emm_v5.o(.text) for Emm_V5_Stop_Now
    uart_bsp.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    uart_bsp.o(.text) refers to strncmp.o(.text) for strncmp
    uart_bsp.o(.text) refers to __0sscanf.o(.text) for __0sscanf
    uart_bsp.o(.text) refers to uart_bsp.o(.data) for motor_angle_limit_check_enabled
    uart_bsp.o(.text) refers to usart.o(.bss) for huart1
    oled_app.o(.text) refers to assert.o(.text) for __aeabi_assert
    oled_app.o(.text) refers to oled.o(.text) for OLED_Init
    oled_app.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_Delay
    oled_app.o(.text) refers to oled_driver.o(.text) for Oled_Printf
    oled_app.o(.text) refers to pi_bsp.o(.text) for GetVisionDataStats
    oled_app.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(.text) refers to oled_app.o(.data) for last_update
    key_bsp.o(.text) refers to assert.o(.text) for __aeabi_assert
    key_bsp.o(.text) refers to key_bsp.o(.data) for key_val
    oled.o(.text) refers to assert.o(.text) for __aeabi_assert
    oled.o(.text) refers to stm32f4xx_hal_i2c.o(.text) for HAL_I2C_Mem_Write
    oled.o(.text) refers to stm32f4xx_hal.o(.text) for HAL_Delay
    oled.o(.text) refers to i2c.o(.bss) for hi2c3
    oled.o(.text) refers to oled.o(.data) for CMD_Data
    oled.o(.text) refers to oled.o(.constdata) for Hzk
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    assert.o(.text) refers to assert_puts.o(.text) for __assert_puts
    assert.o(.text) refers to abort.o(.text) for abort
    strtok_r.o(.text) refers to strtok_int.o(.text) for __strtok_internal
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod.o(i.__hardfp_fmod) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to _rserrno.o(.text) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod_x.o(i.____hardfp_fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.____hardfp_fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.____hardfp_fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.____hardfp_fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    fmod_x.o(i.____softfp_fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.____softfp_fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.____softfp_fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.____softfp_fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    fmod_x.o(i.__fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.__fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.__fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.__fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    assert_puts.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    strtok_int.o(.text) refers to strspn.o(.text) for strspn
    strtok_int.o(.text) refers to strcspn.o(.text) for strcspn
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to scanf1.o(x$fpl$scanf1) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (1148 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (1076 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (960 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (4232 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (504 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (256 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (660 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing encoder_drv.o(.rev16_text), (4 bytes).
    Removing encoder_drv.o(.revsh_text), (4 bytes).
    Removing encoder_drv.o(.rrx_text), (6 bytes).
    Removing encoder_drv.o(.text), (236 bytes).
    Removing mypid.o(.rev16_text), (4 bytes).
    Removing mypid.o(.revsh_text), (4 bytes).
    Removing mypid.o(.rrx_text), (6 bytes).
    Removing mypid.o(.data), (32 bytes).
    Removing oled_driver.o(.rev16_text), (4 bytes).
    Removing oled_driver.o(.revsh_text), (4 bytes).
    Removing oled_driver.o(.rrx_text), (6 bytes).
    Removing encoder_bsp.o(.rev16_text), (4 bytes).
    Removing encoder_bsp.o(.revsh_text), (4 bytes).
    Removing encoder_bsp.o(.rrx_text), (6 bytes).
    Removing encoder_bsp.o(.text), (124 bytes).
    Removing encoder_bsp.o(.bss), (40 bytes).
    Removing pi_bsp.o(.rev16_text), (4 bytes).
    Removing pi_bsp.o(.revsh_text), (4 bytes).
    Removing pi_bsp.o(.rrx_text), (6 bytes).
    Removing schedule.o(.rev16_text), (4 bytes).
    Removing schedule.o(.revsh_text), (4 bytes).
    Removing schedule.o(.rrx_text), (6 bytes).
    Removing step_motor_bsp.o(.rev16_text), (4 bytes).
    Removing step_motor_bsp.o(.revsh_text), (4 bytes).
    Removing step_motor_bsp.o(.rrx_text), (6 bytes).
    Removing uart_bsp.o(.rev16_text), (4 bytes).
    Removing uart_bsp.o(.revsh_text), (4 bytes).
    Removing uart_bsp.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing key_bsp.o(.rev16_text), (4 bytes).
    Removing key_bsp.o(.revsh_text), (4 bytes).
    Removing key_bsp.o(.rrx_text), (6 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled_font.o(.constdata), (192 bytes).
    Removing oled_font.o(.data), (2184 bytes).

131 unused section(s) (total 12222 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_puts.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_int.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/drem.s                          0x00000000   Number         0  drem_clz.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod_x.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\0.96 Oled\oled.c                      0x00000000   Number         0  oled.o ABSOLUTE
    ..\0.96 Oled\oled_font.c                 0x00000000   Number         0  oled_font.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\0.96 Oled\\oled.c                    0x00000000   Number         0  oled.o ABSOLUTE
    ..\\app\\Emm_V5.c                        0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\\app\\encoder_drv.c                   0x00000000   Number         0  encoder_drv.o ABSOLUTE
    ..\\app\\mypid.c                         0x00000000   Number         0  mypid.o ABSOLUTE
    ..\\app\\oled_driver.c                   0x00000000   Number         0  oled_driver.o ABSOLUTE
    ..\\bsp\\encoder_bsp.c                   0x00000000   Number         0  encoder_bsp.o ABSOLUTE
    ..\\bsp\\key_bsp.c                       0x00000000   Number         0  key_bsp.o ABSOLUTE
    ..\\bsp\\oled_app.c                      0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\bsp\\pi_bsp.c                        0x00000000   Number         0  pi_bsp.o ABSOLUTE
    ..\\bsp\\schedule.c                      0x00000000   Number         0  schedule.o ABSOLUTE
    ..\\bsp\\step_motor_bsp.c                0x00000000   Number         0  step_motor_bsp.o ABSOLUTE
    ..\\bsp\\uart_bsp.c                      0x00000000   Number         0  uart_bsp.o ABSOLUTE
    ..\app\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\app\encoder_drv.c                     0x00000000   Number         0  encoder_drv.o ABSOLUTE
    ..\app\motor_driver.c                    0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\app\mypid.c                           0x00000000   Number         0  mypid.o ABSOLUTE
    ..\app\oled_driver.c                     0x00000000   Number         0  oled_driver.o ABSOLUTE
    ..\bsp\encoder_bsp.c                     0x00000000   Number         0  encoder_bsp.o ABSOLUTE
    ..\bsp\key_bsp.c                         0x00000000   Number         0  key_bsp.o ABSOLUTE
    ..\bsp\oled_app.c                        0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\bsp\pi_bsp.c                          0x00000000   Number         0  pi_bsp.o ABSOLUTE
    ..\bsp\schedule.c                        0x00000000   Number         0  schedule.o ABSOLUTE
    ..\bsp\step_motor_bsp.c                  0x00000000   Number         0  step_motor_bsp.o ABSOLUTE
    ..\bsp\uart_bsp.c                        0x00000000   Number         0  uart_bsp.o ABSOLUTE
    ..\ringbuffer\ringbuffer.c               0x00000000   Number         0  ringbuffer.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x0800023c   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000242   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000248   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800024e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000254   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800025a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000260   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800026a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000270   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000276   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800027c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000282   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000288   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800028e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000294   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800029a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002a0   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002a6   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002b0   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002b6   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002bc   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002c2   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002c8   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002cc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002ce   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002d2   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002d8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002d8   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002e4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ee   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002f0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002f2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002f4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002f4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002f4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002fa   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002fa   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002fe   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002fe   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000306   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000308   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000308   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800030c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000314   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x08000314   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000354   Section        0  main.o(.text)
    rt_ringbuffer_get_size                   0x08000355   Thumb Code    24  main.o(.text)
    .text                                    0x08000518   Section        0  gpio.o(.text)
    .text                                    0x080005d8   Section        0  dma.o(.text)
    .text                                    0x08000660   Section        0  i2c.o(.text)
    .text                                    0x08000790   Section        0  tim.o(.text)
    .text                                    0x08000b48   Section        0  usart.o(.text)
    rt_ringbuffer_get_size                   0x08000b49   Thumb Code    24  usart.o(.text)
    .text                                    0x080012a8   Section        0  stm32f4xx_it.o(.text)
    rt_ringbuffer_get_size                   0x080012a9   Thumb Code    24  stm32f4xx_it.o(.text)
    .text                                    0x080013e8   Section        0  stm32f4xx_hal_msp.o(.text)
    .text                                    0x08001430   Section        0  stm32f4xx_hal_i2c.o(.text)
    I2C_Flush_DR                             0x0800162f   Thumb Code    18  stm32f4xx_hal_i2c.o(.text)
    I2C_IsAcknowledgeFailed                  0x08001641   Thumb Code    62  stm32f4xx_hal_i2c.o(.text)
    I2C_WaitOnBTFFlagUntilTimeout            0x0800167f   Thumb Code   102  stm32f4xx_hal_i2c.o(.text)
    I2C_WaitOnTXEFlagUntilTimeout            0x080016e5   Thumb Code   102  stm32f4xx_hal_i2c.o(.text)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800174b   Thumb Code   250  stm32f4xx_hal_i2c.o(.text)
    I2C_WaitOnFlagUntilTimeout               0x08001845   Thumb Code   208  stm32f4xx_hal_i2c.o(.text)
    I2C_MasterRequestWrite                   0x08001915   Thumb Code   186  stm32f4xx_hal_i2c.o(.text)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08001b37   Thumb Code   138  stm32f4xx_hal_i2c.o(.text)
    I2C_MasterRequestRead                    0x08001bc1   Thumb Code   308  stm32f4xx_hal_i2c.o(.text)
    I2C_WaitOnSTOPFlagUntilTimeout           0x0800218f   Thumb Code    98  stm32f4xx_hal_i2c.o(.text)
    I2C_DMAError                             0x08002665   Thumb Code    80  stm32f4xx_hal_i2c.o(.text)
    I2C_DMAXferCplt                          0x080026bd   Thumb Code   328  stm32f4xx_hal_i2c.o(.text)
    I2C_RequestMemoryWrite                   0x08002dd7   Thumb Code   220  stm32f4xx_hal_i2c.o(.text)
    I2C_RequestMemoryRead                    0x0800300f   Thumb Code   368  stm32f4xx_hal_i2c.o(.text)
    I2C_DMAAbort                             0x08004489   Thumb Code   242  stm32f4xx_hal_i2c.o(.text)
    I2C_ITError                              0x08004a37   Thumb Code   420  stm32f4xx_hal_i2c.o(.text)
    I2C_SlaveReceive_BTF                     0x08004c59   Thumb Code    26  stm32f4xx_hal_i2c.o(.text)
    I2C_SlaveReceive_RXNE                    0x08004c73   Thumb Code    70  stm32f4xx_hal_i2c.o(.text)
    I2C_SlaveTransmit_BTF                    0x08004cb9   Thumb Code    44  stm32f4xx_hal_i2c.o(.text)
    I2C_SlaveTransmit_TXE                    0x08004ce5   Thumb Code    70  stm32f4xx_hal_i2c.o(.text)
    I2C_Slave_STOPF                          0x08004d2b   Thumb Code   386  stm32f4xx_hal_i2c.o(.text)
    I2C_Slave_ADDR                           0x08004eaf   Thumb Code   112  stm32f4xx_hal_i2c.o(.text)
    I2C_MasterReceive_BTF                    0x08004f1f   Thumb Code   304  stm32f4xx_hal_i2c.o(.text)
    I2C_WaitOnSTOPRequestThroughIT           0x0800504f   Thumb Code    74  stm32f4xx_hal_i2c.o(.text)
    I2C_MasterReceive_RXNE                   0x08005099   Thumb Code   272  stm32f4xx_hal_i2c.o(.text)
    I2C_MemoryTransmit_TXE_BTF               0x080051ab   Thumb Code   198  stm32f4xx_hal_i2c.o(.text)
    I2C_MasterTransmit_BTF                   0x08005273   Thumb Code   160  stm32f4xx_hal_i2c.o(.text)
    I2C_MasterTransmit_TXE                   0x08005313   Thumb Code   216  stm32f4xx_hal_i2c.o(.text)
    I2C_Master_ADDR                          0x080053eb   Thumb Code   616  stm32f4xx_hal_i2c.o(.text)
    I2C_Master_ADD10                         0x08005653   Thumb Code    42  stm32f4xx_hal_i2c.o(.text)
    I2C_Master_SB                            0x0800567d   Thumb Code   158  stm32f4xx_hal_i2c.o(.text)
    I2C_ConvertOtherXferOptions              0x0800571b   Thumb Code    28  stm32f4xx_hal_i2c.o(.text)
    I2C_Slave_AF                             0x08005911   Thumb Code   162  stm32f4xx_hal_i2c.o(.text)
    .text                                    0x08005aa0   Section        0  stm32f4xx_hal_rcc.o(.text)
    .text                                    0x080063e8   Section        0  stm32f4xx_hal_gpio.o(.text)
    .text                                    0x0800679c   Section        0  stm32f4xx_hal_dma.o(.text)
    DMA_CalcBaseAndBitshift                  0x0800679d   Thumb Code    46  stm32f4xx_hal_dma.o(.text)
    DMA_CheckFifoParam                       0x080067cb   Thumb Code   170  stm32f4xx_hal_dma.o(.text)
    DMA_SetConfig                            0x080069cd   Thumb Code    44  stm32f4xx_hal_dma.o(.text)
    .text                                    0x08007050   Section        0  stm32f4xx_hal_cortex.o(.text)
    __NVIC_SetPriorityGrouping               0x08007051   Thumb Code    30  stm32f4xx_hal_cortex.o(.text)
    __NVIC_GetPriorityGrouping               0x0800706f   Thumb Code    10  stm32f4xx_hal_cortex.o(.text)
    __NVIC_EnableIRQ                         0x08007079   Thumb Code    34  stm32f4xx_hal_cortex.o(.text)
    __NVIC_DisableIRQ                        0x0800709b   Thumb Code    30  stm32f4xx_hal_cortex.o(.text)
    __NVIC_GetPendingIRQ                     0x080070b9   Thumb Code    40  stm32f4xx_hal_cortex.o(.text)
    __NVIC_SetPendingIRQ                     0x080070e1   Thumb Code    24  stm32f4xx_hal_cortex.o(.text)
    __NVIC_ClearPendingIRQ                   0x080070f9   Thumb Code    22  stm32f4xx_hal_cortex.o(.text)
    __NVIC_GetActive                         0x0800710f   Thumb Code    40  stm32f4xx_hal_cortex.o(.text)
    __NVIC_SetPriority                       0x08007137   Thumb Code    34  stm32f4xx_hal_cortex.o(.text)
    __NVIC_GetPriority                       0x08007159   Thumb Code    30  stm32f4xx_hal_cortex.o(.text)
    NVIC_EncodePriority                      0x08007177   Thumb Code    64  stm32f4xx_hal_cortex.o(.text)
    NVIC_DecodePriority                      0x080071b7   Thumb Code    74  stm32f4xx_hal_cortex.o(.text)
    __NVIC_SystemReset                       0x08007201   Thumb Code    32  stm32f4xx_hal_cortex.o(.text)
    SysTick_Config                           0x08007221   Thumb Code    50  stm32f4xx_hal_cortex.o(.text)
    .text                                    0x08007420   Section        0  stm32f4xx_hal.o(.text)
    .text                                    0x08007630   Section        0  stm32f4xx_hal_tim.o(.text)
    TIM_DMAPeriodElapsedHalfCplt             0x0800799f   Thumb Code    14  stm32f4xx_hal_tim.o(.text)
    TIM_DMAPeriodElapsedCplt                 0x080079af   Thumb Code    26  stm32f4xx_hal_tim.o(.text)
    TIM_DMADelayPulseCplt                    0x08008041   Thumb Code   104  stm32f4xx_hal_tim.o(.text)
    TIM_OC4_SetConfig                        0x0800a221   Thumb Code    88  stm32f4xx_hal_tim.o(.text)
    TIM_OC3_SetConfig                        0x0800a279   Thumb Code   112  stm32f4xx_hal_tim.o(.text)
    TIM_OC1_SetConfig                        0x0800a35b   Thumb Code   104  stm32f4xx_hal_tim.o(.text)
    TIM_TI4_SetConfig                        0x0800a433   Thumb Code    60  stm32f4xx_hal_tim.o(.text)
    TIM_TI3_SetConfig                        0x0800a46f   Thumb Code    56  stm32f4xx_hal_tim.o(.text)
    TIM_TI2_SetConfig                        0x0800a4a7   Thumb Code    58  stm32f4xx_hal_tim.o(.text)
    TIM_DMATriggerHalfCplt                   0x0800a88d   Thumb Code    14  stm32f4xx_hal_tim.o(.text)
    TIM_DMATriggerCplt                       0x0800a89b   Thumb Code    26  stm32f4xx_hal_tim.o(.text)
    TIM_ITRx_SetConfig                       0x0800af1b   Thumb Code    18  stm32f4xx_hal_tim.o(.text)
    TIM_TI1_ConfigInputStage                 0x0800af2d   Thumb Code    38  stm32f4xx_hal_tim.o(.text)
    TIM_TI2_ConfigInputStage                 0x0800af53   Thumb Code    40  stm32f4xx_hal_tim.o(.text)
    TIM_SlaveTimer_SetConfig                 0x0800b09d   Thumb Code   178  stm32f4xx_hal_tim.o(.text)
    .text                                    0x0800b2bc   Section        0  stm32f4xx_hal_tim_ex.o(.text)
    TIM_CCxNChannelCmd                       0x0800b77b   Thumb Code    34  stm32f4xx_hal_tim_ex.o(.text)
    TIM_DMAErrorCCxN                         0x0800bb31   Thumb Code    68  stm32f4xx_hal_tim_ex.o(.text)
    TIM_DMADelayPulseNCplt                   0x0800bb75   Thumb Code    82  stm32f4xx_hal_tim_ex.o(.text)
    .text                                    0x0800ca90   Section        0  stm32f4xx_hal_uart.o(.text)
    UART_SetConfig                           0x0800ca91   Thumb Code   546  stm32f4xx_hal_uart.o(.text)
    UART_EndRxTransfer                       0x0800cf1d   Thumb Code   108  stm32f4xx_hal_uart.o(.text)
    UART_WaitOnFlagUntilTimeout              0x0800cf89   Thumb Code   140  stm32f4xx_hal_uart.o(.text)
    UART_EndTxTransfer                       0x0800d23b   Thumb Code    38  stm32f4xx_hal_uart.o(.text)
    UART_DMAError                            0x0800d261   Thumb Code    80  stm32f4xx_hal_uart.o(.text)
    UART_DMATxHalfCplt                       0x0800d2b3   Thumb Code    14  stm32f4xx_hal_uart.o(.text)
    UART_DMATransmitCplt                     0x0800d2c3   Thumb Code    88  stm32f4xx_hal_uart.o(.text)
    UART_DMARxHalfCplt                       0x0800d3af   Thumb Code    36  stm32f4xx_hal_uart.o(.text)
    UART_DMAReceiveCplt                      0x0800d3d5   Thumb Code   180  stm32f4xx_hal_uart.o(.text)
    UART_DMARxAbortCallback                  0x0800dbdd   Thumb Code    56  stm32f4xx_hal_uart.o(.text)
    UART_DMATxAbortCallback                  0x0800dc15   Thumb Code    56  stm32f4xx_hal_uart.o(.text)
    UART_DMATxOnlyAbortCallback              0x0800dd97   Thumb Code    24  stm32f4xx_hal_uart.o(.text)
    UART_DMARxOnlyAbortCallback              0x0800de3d   Thumb Code    28  stm32f4xx_hal_uart.o(.text)
    UART_EndTransmit_IT                      0x0800df2f   Thumb Code    32  stm32f4xx_hal_uart.o(.text)
    UART_Transmit_IT                         0x0800df4f   Thumb Code    96  stm32f4xx_hal_uart.o(.text)
    UART_DMAAbortOnError                     0x0800dfaf   Thumb Code    18  stm32f4xx_hal_uart.o(.text)
    UART_Receive_IT                          0x0800dfc1   Thumb Code   268  stm32f4xx_hal_uart.o(.text)
    .text                                    0x0800e56c   Section        0  system_stm32f4xx.o(.text)
    .text                                    0x0800e634   Section        0  ringbuffer.o(.text)
    rt_ringbuffer_get_size                   0x0800e635   Thumb Code    24  ringbuffer.o(.text)
    rt_ringbuffer_status                     0x0800e64d   Thumb Code    42  ringbuffer.o(.text)
    .text                                    0x0800ebe0   Section        0  emm_v5.o(.text)
    rt_ringbuffer_get_size                   0x0800ebe1   Thumb Code    24  emm_v5.o(.text)
    .text                                    0x0800f444   Section        0  mypid.o(.text)
    rt_ringbuffer_get_size                   0x0800f445   Thumb Code    24  mypid.o(.text)
    pid_reset                                0x0800f45d   Thumb Code    46  mypid.o(.text)
    pid_param_init                           0x0800f48b   Thumb Code    20  mypid.o(.text)
    .text                                    0x0801013c   Section        0  oled_driver.o(.text)
    rt_ringbuffer_get_size                   0x0801013d   Thumb Code    24  oled_driver.o(.text)
    .text                                    0x080101f4   Section        0  pi_bsp.o(.text)
    rt_ringbuffer_get_size                   0x080101f5   Thumb Code    24  pi_bsp.o(.text)
    .text                                    0x0801048c   Section        0  schedule.o(.text)
    rt_ringbuffer_get_size                   0x0801048d   Thumb Code    24  schedule.o(.text)
    .text                                    0x08010530   Section        0  step_motor_bsp.o(.text)
    rt_ringbuffer_get_size                   0x08010531   Thumb Code    24  step_motor_bsp.o(.text)
    .text                                    0x080107d4   Section        0  uart_bsp.o(.text)
    rt_ringbuffer_get_size                   0x080107d5   Thumb Code    24  uart_bsp.o(.text)
    .text                                    0x08010c78   Section        0  oled_app.o(.text)
    rt_ringbuffer_get_size                   0x08010c79   Thumb Code    24  oled_app.o(.text)
    .text                                    0x08010efc   Section        0  key_bsp.o(.text)
    rt_ringbuffer_get_size                   0x08010efd   Thumb Code    24  key_bsp.o(.text)
    .text                                    0x08010f90   Section        0  oled.o(.text)
    rt_ringbuffer_get_size                   0x08010f91   Thumb Code    24  oled.o(.text)
    .text                                    0x08011658   Section      238  lludivv7m.o(.text)
    .text                                    0x08011748   Section        0  vsnprintf.o(.text)
    .text                                    0x0801177c   Section        0  __0sscanf.o(.text)
    .text                                    0x080117b8   Section        0  _scanf_int.o(.text)
    .text                                    0x08011904   Section        0  atoi.o(.text)
    .text                                    0x08011920   Section        0  assert.o(.text)
    .text                                    0x080119a0   Section        0  strtok_r.o(.text)
    .text                                    0x080119a4   Section        0  strncmp.o(.text)
    .text                                    0x08011a3a   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08011ac4   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08011b28   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08011b78   Section      128  strcmpv7m.o(.text)
    .text                                    0x08011bf8   Section        0  heapauxi.o(.text)
    .text                                    0x08011c00   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08011c08   Section        0  _rserrno.o(.text)
    .text                                    0x08011c1e   Section        0  _printf_pad.o(.text)
    .text                                    0x08011c6c   Section        0  _printf_truncate.o(.text)
    .text                                    0x08011c90   Section        0  _printf_str.o(.text)
    .text                                    0x08011ce4   Section        0  _printf_dec.o(.text)
    .text                                    0x08011d5c   Section        0  _printf_charcount.o(.text)
    .text                                    0x08011d84   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08011d85   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08011db4   Section        0  _sputc.o(.text)
    .text                                    0x08011dbe   Section        0  _snputc.o(.text)
    .text                                    0x08011dd0   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08011e8c   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08011f08   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08011f09   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08011f78   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08011f79   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x0801200c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08012194   Section        0  _chval.o(.text)
    .text                                    0x080121b0   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x080121b1   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x080121dc   Section        0  _sgetc.o(.text)
    .text                                    0x0801221c   Section        0  strtod.o(.text)
    _local_sscanf                            0x0801221d   Thumb Code    60  strtod.o(.text)
    .text                                    0x080122c0   Section        0  strtol.o(.text)
    .text                                    0x08012330   Section        0  abort.o(.text)
    .text                                    0x08012346   Section        0  assert_puts.o(.text)
    .text                                    0x0801235c   Section        0  strtok_int.o(.text)
    .text                                    0x080123a0   Section        0  sys_wrch.o(.text)
    .text                                    0x080123b0   Section        0  sys_exit.o(.text)
    .text                                    0x080123bc   Section        8  libspace.o(.text)
    .text                                    0x080123c4   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080123d4   Section      138  lludiv10.o(.text)
    .text                                    0x0801245e   Section        0  isspace.o(.text)
    .text                                    0x08012470   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08012522   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08012525   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08012940   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08012c3c   Section        0  _printf_char.o(.text)
    .text                                    0x08012c68   Section        0  _printf_wchar.o(.text)
    .text                                    0x08012c94   Section        0  _scanf.o(.text)
    .text                                    0x08013008   Section        0  _strtoul.o(.text)
    .text                                    0x080130a6   Section        0  _wcrtomb.o(.text)
    .text                                    0x080130e6   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x080130f4   Section        0  strcspn.o(.text)
    .text                                    0x08013114   Section        0  strspn.o(.text)
    .text                                    0x08013130   Section        2  use_no_semi.o(.text)
    .text                                    0x08013132   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08013132   Section        0  indicate_semi.o(.text)
    .text                                    0x0801317c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08013184   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08013204   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08013205   Thumb Code   588  scanf_fp.o(.text)
    .text                                    0x080136fc   Section        0  bigflt0.o(.text)
    .text                                    0x080137e0   Section        0  exit.o(.text)
    .text                                    0x080137f2   Section        0  defsig_exit.o(.text)
    .text                                    0x080137fc   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x0801382c   Section        0  defsig_general.o(.text)
    .text                                    0x08013860   Section        0  scanf_hexfp.o(.text)
    .text                                    0x08013b80   Section        0  scanf_infnan.o(.text)
    .text                                    0x08013cb4   Section       38  llshl.o(.text)
    CL$$btod_d2e                             0x08013cda   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08013d18   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08013d5e   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08013dbe   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x080140f8   Section      132  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x0801417c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08014258   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x08014282   Section       42  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x080142ac   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x080142d6   Section       42  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x08014300   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08014544   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp___mathlib_tofloat             0x08014578   Section        0  narrow.o(i.__hardfp___mathlib_tofloat)
    i.__hardfp_atof                          0x08014670   Section        0  atof.o(i.__hardfp_atof)
    i.__hardfp_fabs                          0x080146a8   Section        0  fabs.o(i.__hardfp_fabs)
    i.__hardfp_fmod                          0x080146bc   Section        0  fmod.o(i.__hardfp_fmod)
    i.__hardfp_ldexp                         0x080147c0   Section        0  ldexp.o(i.__hardfp_ldexp)
    i.__mathlib_dbl_invalid                  0x08014890   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x080148b0   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x080148d0   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_narrow                       0x080148f0   Section        0  narrow.o(i.__mathlib_narrow)
    i.__support_ldexp                        0x08014902   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x08014916   Section        0  __printf_wp.o(i._is_digit)
    i.frexp                                  0x08014928   Section        0  frexp.o(i.frexp)
    locale$$code                             0x080149b4   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080149e0   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x08014a0c   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08014a0c   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08014a70   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08014a70   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08014a81   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08014bc0   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x08014bc0   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x08014bd0   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08014bd0   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08014be8   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08014be8   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08014bef   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x08014e98   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x08014e98   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dleqf                              0x08014f10   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08014f10   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08014f88   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08014f88   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080150dc   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x080150dc   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$drem                               0x08015178   Section      336  drem_clz.o(x$fpl$drem)
    $v0                                      0x08015178   Number         0  drem_clz.o(x$fpl$drem)
    x$fpl$dretinf                            0x080152c8   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x080152c8   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x080152d4   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x080152d4   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08015340   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08015340   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x08015358   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08015358   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08015369   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0801552c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0801552c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08015582   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08015582   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0801560e   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0801560e   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08015618   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08015618   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$ieeestatus                         0x08015622   Section        6  istatus.o(x$fpl$ieeestatus)
    $v0                                      0x08015622   Number         0  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x08015628   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08015628   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x0801562c   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x0801562c   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x08015630   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x08015630   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08015694   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x08015694   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x080156f0   Section        4  scanf1.o(x$fpl$scanf1)
    $v0                                      0x080156f0   Number         0  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x080156f4   Section        8  scanf2.o(x$fpl$scanf2)
    $v0                                      0x080156f4   Number         0  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x080156fc   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x080156fc   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0801572c   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x0801572c   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x0801572c   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08015734   Section       24  system_stm32f4xx.o(.constdata)
    .constdata                               0x0801574c   Section      192  oled.o(.constdata)
    Hzk                                      0x0801574c   Data         192  oled.o(.constdata)
    .constdata                               0x0801580c   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0801580c   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08015814   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08015814   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08015828   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0801583c   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0801583c   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0801584d   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0801584d   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08015860   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08015874   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08015874   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080158b0   Data          64  bigflt0.o(.constdata)
    c$$dinf                                  0x08015928   Section        8  fpconst.o(c$$dinf)
    c$$dmax                                  0x08015930   Section        8  fpconst.o(c$$dmax)
    locale$$data                             0x08015938   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0801593c   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08015944   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08015950   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08015952   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08015953   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08015954   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08015954   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08015958   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08015960   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08015a64   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       18  pi_bsp.o(.data)
    total_packets_received                   0x20000014   Data           4  pi_bsp.o(.data)
    valid_packets_parsed                     0x20000018   Data           4  pi_bsp.o(.data)
    last_packet_time                         0x2000001c   Data           4  pi_bsp.o(.data)
    line_pos                                 0x20000020   Data           2  pi_bsp.o(.data)
    .data                                    0x20000024   Section       52  schedule.o(.data)
    schedule_task                            0x20000028   Data          48  schedule.o(.data)
    .data                                    0x20000058   Section       43  uart_bsp.o(.data)
    .data                                    0x20000084   Section        8  oled_app.o(.data)
    last_update                              0x20000084   Data           4  oled_app.o(.data)
    frame_counter                            0x20000088   Data           4  oled_app.o(.data)
    .data                                    0x2000008c   Section        4  key_bsp.o(.data)
    .data                                    0x20000090   Section     2207  oled.o(.data)
    F6x8                                     0x20000090   Data         552  oled.o(.data)
    F8X16                                    0x200002b8   Data        1504  oled.o(.data)
    BMP1                                     0x20000898   Data         128  oled.o(.data)
    .bss                                     0x20000930   Section       84  i2c.o(.bss)
    .bss                                     0x20000984   Section      216  tim.o(.bss)
    .bss                                     0x20000a5c   Section     1168  usart.o(.bss)
    .bss                                     0x20000eec   Section      480  mypid.o(.bss)
    .bss                                     0x200010cc   Section      636  pi_bsp.o(.bss)
    line_buffer                              0x20001148   Data         512  pi_bsp.o(.bss)
    .bss                                     0x20001348   Section      612  uart_bsp.o(.bss)
    .bss                                     0x200015ac   Section       96  libspace.o(.bss)
    HEAP                                     0x20001610   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20001610   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20001810   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20001810   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20001c10   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x0800023d   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000243   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000249   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800024f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000255   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800025b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000261   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800026b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000271   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000277   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800027d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000283   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000289   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800028f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000295   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800029b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002a1   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002a7   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002b1   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002b7   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002bd   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002c3   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002c9   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002cd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002d9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002d9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002f1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002f5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002f5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002f5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000307   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000309   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000309   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800030d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000315   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08000331   Thumb Code     0  startup_stm32f407xx.o(.text)
    Error_Handler                            0x0800036d   Thumb Code     6  main.o(.text)
    SystemClock_Config                       0x08000373   Thumb Code   150  main.o(.text)
    main                                     0x08000409   Thumb Code   172  main.o(.text)
    MX_GPIO_Init                             0x08000519   Thumb Code   186  gpio.o(.text)
    MX_DMA_Init                              0x080005d9   Thumb Code   130  dma.o(.text)
    MX_I2C3_Init                             0x08000661   Thumb Code    48  i2c.o(.text)
    HAL_I2C_MspInit                          0x08000691   Thumb Code   182  i2c.o(.text)
    HAL_I2C_MspDeInit                        0x08000747   Thumb Code    48  i2c.o(.text)
    HAL_TIM_MspPostInit                      0x08000791   Thumb Code    86  tim.o(.text)
    MX_TIM1_Init                             0x080007e7   Thumb Code   222  tim.o(.text)
    MX_TIM3_Init                             0x080008c5   Thumb Code   114  tim.o(.text)
    MX_TIM4_Init                             0x08000937   Thumb Code   114  tim.o(.text)
    HAL_TIM_Base_MspInit                     0x080009a9   Thumb Code    48  tim.o(.text)
    HAL_TIM_Encoder_MspInit                  0x080009d9   Thumb Code   226  tim.o(.text)
    HAL_TIM_Base_MspDeInit                   0x08000abb   Thumb Code    26  tim.o(.text)
    HAL_TIM_Encoder_MspDeInit                0x08000ad5   Thumb Code    74  tim.o(.text)
    HAL_UARTEx_RxEventCallback               0x08000b61   Thumb Code    86  usart.o(.text)
    MX_UART4_Init                            0x08000bb7   Thumb Code    46  usart.o(.text)
    MX_UART5_Init                            0x08000be5   Thumb Code    46  usart.o(.text)
    MX_USART1_UART_Init                      0x08000c13   Thumb Code    46  usart.o(.text)
    MX_USART2_UART_Init                      0x08000c41   Thumb Code    46  usart.o(.text)
    MX_USART3_UART_Init                      0x08000c6f   Thumb Code    46  usart.o(.text)
    MX_USART6_UART_Init                      0x08000c9d   Thumb Code    46  usart.o(.text)
    HAL_UART_MspInit                         0x08000ccb   Thumb Code  1180  usart.o(.text)
    HAL_UART_MspDeInit                       0x08001167   Thumb Code   258  usart.o(.text)
    NMI_Handler                              0x080012c1   Thumb Code     4  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x080012c5   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080012c9   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x080012cd   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x080012d1   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x080012d5   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x080012d7   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x080012d9   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x080012db   Thumb Code     8  stm32f4xx_it.o(.text)
    DMA1_Stream1_IRQHandler                  0x080012e3   Thumb Code    10  stm32f4xx_it.o(.text)
    DMA1_Stream2_IRQHandler                  0x080012ed   Thumb Code    10  stm32f4xx_it.o(.text)
    DMA1_Stream5_IRQHandler                  0x080012f7   Thumb Code    10  stm32f4xx_it.o(.text)
    USART1_IRQHandler                        0x08001301   Thumb Code    10  stm32f4xx_it.o(.text)
    USART2_IRQHandler                        0x0800130b   Thumb Code    36  stm32f4xx_it.o(.text)
    USART3_IRQHandler                        0x0800132f   Thumb Code    36  stm32f4xx_it.o(.text)
    UART4_IRQHandler                         0x08001353   Thumb Code    10  stm32f4xx_it.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800135d   Thumb Code    10  stm32f4xx_it.o(.text)
    USART6_IRQHandler                        0x08001367   Thumb Code    36  stm32f4xx_it.o(.text)
    HAL_MspInit                              0x080013e9   Thumb Code    68  stm32f4xx_hal_msp.o(.text)
    HAL_I2C_Init                             0x08001433   Thumb Code   446  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_DeInit                           0x080015f3   Thumb Code    60  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Transmit                  0x080019cf   Thumb Code   360  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Receive                   0x08001cf5   Thumb Code   778  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Transmit                   0x08001fff   Thumb Code   400  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Receive                    0x080021f1   Thumb Code   386  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Transmit_IT               0x08002373   Thumb Code   224  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Receive_IT                0x08002453   Thumb Code   236  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Transmit_IT                0x0800253f   Thumb Code   146  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Receive_IT                 0x080025d1   Thumb Code   146  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_ErrorCallback                    0x08002663   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_MasterRxCpltCallback             0x080026b5   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_MemRxCpltCallback                0x080026b7   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_SlaveRxCpltCallback              0x080026b9   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_SlaveTxCpltCallback              0x080026bb   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Transmit_DMA              0x08002805   Thumb Code   442  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Receive_DMA               0x080029bf   Thumb Code   442  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Transmit_DMA               0x08002b79   Thumb Code   312  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Receive_DMA                0x08002cb1   Thumb Code   294  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Mem_Write                        0x08002eb3   Thumb Code   348  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Mem_Read                         0x0800317f   Thumb Code   766  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Mem_Write_IT                     0x0800347d   Thumb Code   254  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Mem_Read_IT                      0x0800357b   Thumb Code   254  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Mem_Write_DMA                    0x08003679   Thumb Code   522  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Mem_Read_DMA                     0x08003883   Thumb Code   614  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_IsDeviceReady                    0x08003ae9   Thumb Code   430  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Seq_Transmit_IT           0x08003c97   Thumb Code   284  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Seq_Transmit_DMA          0x08003db3   Thumb Code   556  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Seq_Receive_IT            0x08003fdf   Thumb Code   360  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Seq_Receive_DMA           0x08004147   Thumb Code   662  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Seq_Transmit_IT            0x080043dd   Thumb Code   170  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_AbortCpltCallback                0x08004487   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Seq_Transmit_DMA           0x0800457b   Thumb Code   446  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Seq_Receive_IT             0x08004739   Thumb Code   158  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Slave_Seq_Receive_DMA            0x080047d7   Thumb Code   466  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_EnableListen_IT                  0x080049a9   Thumb Code    70  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_DisableListen_IT                 0x080049ef   Thumb Code    70  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_ListenCpltCallback               0x08004a35   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_Master_Abort_IT                  0x08004bdb   Thumb Code   126  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_AddrCallback                     0x08004ead   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_MemTxCpltCallback                0x080051a9   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_MasterTxCpltCallback             0x08005271   Thumb Code     2  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_EV_IRQHandler                    0x08005737   Thumb Code   474  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_ER_IRQHandler                    0x080059b3   Thumb Code   212  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_GetState                         0x08005a87   Thumb Code     8  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_GetMode                          0x08005a8f   Thumb Code     8  stm32f4xx_hal_i2c.o(.text)
    HAL_I2C_GetError                         0x08005a97   Thumb Code     6  stm32f4xx_hal_i2c.o(.text)
    HAL_RCC_OscConfig                        0x08005aa5   Thumb Code  1172  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetSysClockFreq                  0x08005f39   Thumb Code   164  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_ClockConfig                      0x08005fdd   Thumb Code   390  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_MCOConfig                        0x08006163   Thumb Code   186  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_EnableCSS                        0x0800621d   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_DisableCSS                       0x08006225   Thumb Code     8  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetHCLKFreq                      0x0800622d   Thumb Code     6  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK1Freq                     0x08006233   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetPCLK2Freq                     0x0800624b   Thumb Code    24  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetOscConfig                     0x08006263   Thumb Code   284  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_GetClockConfig                   0x0800637f   Thumb Code    66  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_CSSCallback                      0x080063c1   Thumb Code     2  stm32f4xx_hal_rcc.o(.text)
    HAL_RCC_NMI_IRQHandler                   0x080063c3   Thumb Code    30  stm32f4xx_hal_rcc.o(.text)
    HAL_GPIO_Init                            0x080063e9   Thumb Code   454  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_DeInit                          0x080065af   Thumb Code   324  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_ReadPin                         0x080066f3   Thumb Code    16  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_WritePin                        0x08006703   Thumb Code    12  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_TogglePin                       0x0800670f   Thumb Code    20  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_LockPin                         0x08006723   Thumb Code    46  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_EXTI_Callback                   0x08006751   Thumb Code     2  stm32f4xx_hal_gpio.o(.text)
    HAL_GPIO_EXTI_IRQHandler                 0x08006753   Thumb Code    28  stm32f4xx_hal_gpio.o(.text)
    HAL_DMA_Init                             0x08006875   Thumb Code   232  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_DeInit                           0x0800695d   Thumb Code   112  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Start                            0x080069f9   Thumb Code   102  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Start_IT                         0x08006a5f   Thumb Code   146  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Abort                            0x08006af1   Thumb Code   180  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_Abort_IT                         0x08006ba5   Thumb Code    40  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_PollForTransfer                  0x08006bcd   Thumb Code   346  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_IRQHandler                       0x08006d27   Thumb Code   570  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_RegisterCallback                 0x08006f61   Thumb Code    96  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_UnRegisterCallback               0x08006fc1   Thumb Code   124  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_GetState                         0x0800703d   Thumb Code     8  stm32f4xx_hal_dma.o(.text)
    HAL_DMA_GetError                         0x08007045   Thumb Code     6  stm32f4xx_hal_dma.o(.text)
    HAL_NVIC_SetPriorityGrouping             0x08007253   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPriority                     0x0800725f   Thumb Code    42  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_EnableIRQ                       0x08007289   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_DisableIRQ                      0x08007295   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SystemReset                     0x080072a1   Thumb Code     4  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Config                       0x080072a5   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Disable                          0x080072b1   Thumb Code    30  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_Enable                           0x080072cf   Thumb Code    36  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_EnableRegion                     0x080072f3   Thumb Code    24  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_DisableRegion                    0x0800730b   Thumb Code    24  stm32f4xx_hal_cortex.o(.text)
    HAL_MPU_ConfigRegion                     0x08007323   Thumb Code    88  stm32f4xx_hal_cortex.o(.text)
    HAL_CORTEX_ClearEvent                    0x0800737b   Thumb Code     6  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriorityGrouping             0x08007381   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPriority                     0x08007389   Thumb Code    34  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_SetPendingIRQ                   0x080073ab   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetPendingIRQ                   0x080073b7   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_ClearPendingIRQ                 0x080073c3   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_NVIC_GetActive                       0x080073cf   Thumb Code    12  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_CLKSourceConfig              0x080073db   Thumb Code    40  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_Callback                     0x08007403   Thumb Code     2  stm32f4xx_hal_cortex.o(.text)
    HAL_SYSTICK_IRQHandler                   0x08007405   Thumb Code     8  stm32f4xx_hal_cortex.o(.text)
    HAL_InitTick                             0x08007423   Thumb Code    64  stm32f4xx_hal.o(.text)
    HAL_Init                                 0x08007463   Thumb Code    54  stm32f4xx_hal.o(.text)
    HAL_MspDeInit                            0x08007499   Thumb Code     2  stm32f4xx_hal.o(.text)
    HAL_DeInit                               0x0800749b   Thumb Code    62  stm32f4xx_hal.o(.text)
    HAL_IncTick                              0x080074d9   Thumb Code    16  stm32f4xx_hal.o(.text)
    HAL_GetTick                              0x080074e9   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetTickPrio                          0x080074ef   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_SetTickFreq                          0x080074f5   Thumb Code    40  stm32f4xx_hal.o(.text)
    HAL_GetTickFreq                          0x0800751d   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_Delay                                0x08007523   Thumb Code    36  stm32f4xx_hal.o(.text)
    HAL_SuspendTick                          0x08007547   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_ResumeTick                           0x08007559   Thumb Code    18  stm32f4xx_hal.o(.text)
    HAL_GetHalVersion                        0x0800756b   Thumb Code     4  stm32f4xx_hal.o(.text)
    HAL_GetREVID                             0x0800756f   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetDEVID                             0x08007577   Thumb Code    10  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGSleepMode            0x08007581   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGSleepMode           0x0800758f   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStopMode             0x0800759d   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStopMode            0x080075ab   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_EnableDBGStandbyMode          0x080075b9   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_DBGMCU_DisableDBGStandbyMode         0x080075c7   Thumb Code    14  stm32f4xx_hal.o(.text)
    HAL_EnableCompensationCell               0x080075d5   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_DisableCompensationCell              0x080075dd   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw0                             0x080075e5   Thumb Code     6  stm32f4xx_hal.o(.text)
    HAL_GetUIDw1                             0x080075eb   Thumb Code     8  stm32f4xx_hal.o(.text)
    HAL_GetUIDw2                             0x080075f3   Thumb Code     8  stm32f4xx_hal.o(.text)
    TIM_Base_SetConfig                       0x08007631   Thumb Code   178  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_Init                        0x080076e5   Thumb Code   102  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_DeInit                      0x0800774d   Thumb Code   120  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_Start                       0x080077c5   Thumb Code   126  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_Stop                        0x08007843   Thumb Code    50  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_Start_IT                    0x08007875   Thumb Code   138  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_Stop_IT                     0x080078ff   Thumb Code    62  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_ErrorCallback                    0x0800793d   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    TIM_DMAError                             0x0800793f   Thumb Code    94  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PeriodElapsedHalfCpltCallback    0x0800799d   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PeriodElapsedCallback            0x080079ad   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_Start_DMA                   0x080079c9   Thumb Code   262  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_Stop_DMA                    0x08007acf   Thumb Code    70  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_MspInit                       0x08007b15   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_Init                          0x08007b17   Thumb Code   102  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_MspDeInit                     0x08007b7d   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_DeInit                        0x08007b7f   Thumb Code   120  stm32f4xx_hal_tim.o(.text)
    TIM_CCxChannelCmd                        0x08007bf7   Thumb Code    34  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_Start                         0x08007c19   Thumb Code   238  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_Stop                          0x08007d07   Thumb Code   160  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_Start_IT                      0x08007da7   Thumb Code   354  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_Stop_IT                       0x08007f09   Thumb Code   244  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_PulseFinishedHalfCpltCallback 0x08007ffd   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    TIM_DMADelayPulseHalfCplt                0x08007fff   Thumb Code    64  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_PulseFinishedCallback        0x0800803f   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_Start_DMA                     0x080080a9   Thumb Code   596  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_Stop_DMA                      0x080082fd   Thumb Code   268  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_MspInit                      0x08008409   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_Init                         0x0800840b   Thumb Code   102  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_MspDeInit                    0x08008471   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_DeInit                       0x08008473   Thumb Code   120  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_Start                        0x080084eb   Thumb Code   238  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_Stop                         0x080085d9   Thumb Code   190  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_Start_IT                     0x08008697   Thumb Code   324  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_Stop_IT                      0x080087db   Thumb Code   244  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_Start_DMA                    0x080088cf   Thumb Code   556  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_Stop_DMA                     0x08008afb   Thumb Code   306  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_MspInit                       0x08008c2d   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_Init                          0x08008c2f   Thumb Code   102  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_MspDeInit                     0x08008c95   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_DeInit                        0x08008c97   Thumb Code   120  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_Start                         0x08008d0f   Thumb Code   288  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_Stop                          0x08008e2f   Thumb Code   138  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_Start_IT                      0x08008eb9   Thumb Code   402  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_Stop_IT                       0x0800904b   Thumb Code   222  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_CaptureHalfCpltCallback       0x08009129   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    TIM_DMACaptureHalfCplt                   0x0800912b   Thumb Code    64  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_CaptureCallback               0x0800916b   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    TIM_DMACaptureCplt                       0x0800916d   Thumb Code   122  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_Start_DMA                     0x080091e7   Thumb Code   618  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_Stop_DMA                      0x08009451   Thumb Code   244  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_MspInit                 0x08009545   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_Init                    0x08009547   Thumb Code   102  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_MspDeInit               0x080095ad   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_DeInit                  0x080095af   Thumb Code    96  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_Start                   0x0800960f   Thumb Code   132  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_Stop                    0x08009693   Thumb Code   152  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_Start_IT                0x0800972b   Thumb Code   186  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_Stop_IT                 0x080097e5   Thumb Code   176  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_Init                     0x08009897   Thumb Code   200  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_DeInit                   0x08009961   Thumb Code    96  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_Start                    0x080099c1   Thumb Code   204  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_Stop                     0x08009a8d   Thumb Code   214  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_Start_IT                 0x08009b63   Thumb Code   262  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_Stop_IT                  0x08009c69   Thumb Code   256  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_Start_DMA                0x08009d69   Thumb Code   560  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_Stop_DMA                 0x08009f99   Thumb Code   280  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_TriggerCallback                  0x0800a0b1   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_DelayElapsedCallback          0x0800a0b3   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IRQHandler                       0x0800a0b5   Thumb Code   364  stm32f4xx_hal_tim.o(.text)
    TIM_OC2_SetConfig                        0x0800a2e9   Thumb Code   114  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_ConfigChannel                 0x0800a3c3   Thumb Code   112  stm32f4xx_hal_tim.o(.text)
    TIM_TI1_SetConfig                        0x0800a4e1   Thumb Code   114  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_ConfigChannel                 0x0800a553   Thumb Code   222  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_ConfigChannel                0x0800a631   Thumb Code   290  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_ConfigChannel           0x0800a753   Thumb Code   312  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_TriggerHalfCpltCallback          0x0800a88b   Thumb Code     2  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_DMABurst_MultiWriteStart         0x0800a8b5   Thumb Code   438  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_DMABurst_WriteStart              0x0800aa6b   Thumb Code    42  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_DMABurst_WriteStop               0x0800aa95   Thumb Code   140  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_DMABurst_MultiReadStart          0x0800ab21   Thumb Code   438  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_DMABurst_ReadStart               0x0800acd7   Thumb Code    86  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_DMABurst_ReadStop                0x0800ad2d   Thumb Code   140  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_GenerateEvent                    0x0800adb9   Thumb Code    54  stm32f4xx_hal_tim.o(.text)
    TIM_ETR_SetConfig                        0x0800adef   Thumb Code    22  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_ConfigOCrefClear                 0x0800ae05   Thumb Code   278  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_ConfigClockSource                0x0800af7b   Thumb Code   268  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_ConfigTI1Input                   0x0800b087   Thumb Code    22  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_SlaveConfigSynchro               0x0800b14f   Thumb Code   108  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_SlaveConfigSynchro_IT            0x0800b1bb   Thumb Code   108  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_ReadCapturedValue                0x0800b227   Thumb Code    50  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Base_GetState                    0x0800b259   Thumb Code     8  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OC_GetState                      0x0800b261   Thumb Code     8  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_PWM_GetState                     0x0800b269   Thumb Code     8  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_IC_GetState                      0x0800b271   Thumb Code     8  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_OnePulse_GetState                0x0800b279   Thumb Code     8  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_Encoder_GetState                 0x0800b281   Thumb Code     8  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_GetActiveChannel                 0x0800b289   Thumb Code     6  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_GetChannelState                  0x0800b28f   Thumb Code    38  stm32f4xx_hal_tim.o(.text)
    HAL_TIM_DMABurstState                    0x0800b2b5   Thumb Code     8  stm32f4xx_hal_tim.o(.text)
    HAL_TIMEx_HallSensor_MspInit             0x0800b2bd   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_Init                0x0800b2bf   Thumb Code   234  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_MspDeInit           0x0800b3a9   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_DeInit              0x0800b3ab   Thumb Code    96  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_Start               0x0800b40b   Thumb Code   188  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_Stop                0x0800b4c7   Thumb Code    78  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_Start_IT            0x0800b515   Thumb Code   200  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_Stop_IT             0x0800b5dd   Thumb Code    90  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_Start_DMA           0x0800b637   Thumb Code   240  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_Stop_DMA            0x0800b727   Thumb Code    84  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OCN_Start                      0x0800b79d   Thumb Code   256  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OCN_Stop                       0x0800b89d   Thumb Code   136  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OCN_Start_IT                   0x0800b925   Thumb Code   294  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OCN_Stop_IT                    0x0800ba4b   Thumb Code   230  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OCN_Start_DMA                  0x0800bbc7   Thumb Code   464  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OCN_Stop_DMA                   0x0800bd97   Thumb Code   262  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_PWMN_Start                     0x0800be9d   Thumb Code   214  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_PWMN_Stop                      0x0800bf73   Thumb Code   136  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_PWMN_Start_IT                  0x0800bffb   Thumb Code   294  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_PWMN_Stop_IT                   0x0800c121   Thumb Code   230  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_PWMN_Start_DMA                 0x0800c207   Thumb Code   506  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_PWMN_Stop_DMA                  0x0800c401   Thumb Code   220  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OnePulseN_Start                0x0800c4dd   Thumb Code   118  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OnePulseN_Stop                 0x0800c553   Thumb Code   138  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OnePulseN_Start_IT             0x0800c5dd   Thumb Code   142  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_OnePulseN_Stop_IT              0x0800c66b   Thumb Code   162  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_ConfigCommutEvent              0x0800c70d   Thumb Code   176  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_ConfigCommutEvent_IT           0x0800c7bd   Thumb Code   134  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_CommutHalfCpltCallback         0x0800c843   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text)
    TIMEx_DMACommutationHalfCplt             0x0800c845   Thumb Code    20  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_CommutCallback                 0x0800c859   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text)
    TIMEx_DMACommutationCplt                 0x0800c85b   Thumb Code    20  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_ConfigCommutEvent_DMA          0x0800c86f   Thumb Code   156  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_MasterConfigSynchronization    0x0800c90b   Thumb Code   150  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_ConfigBreakDeadTime            0x0800c9a1   Thumb Code   116  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_RemapConfig                    0x0800ca15   Thumb Code    42  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_BreakCallback                  0x0800ca3f   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_HallSensor_GetState            0x0800ca41   Thumb Code     8  stm32f4xx_hal_tim_ex.o(.text)
    HAL_TIMEx_GetChannelNState               0x0800ca49   Thumb Code    38  stm32f4xx_hal_tim_ex.o(.text)
    HAL_UART_Init                            0x0800ccb5   Thumb Code   118  stm32f4xx_hal_uart.o(.text)
    HAL_HalfDuplex_Init                      0x0800cd2b   Thumb Code   122  stm32f4xx_hal_uart.o(.text)
    HAL_LIN_Init                             0x0800cda5   Thumb Code   146  stm32f4xx_hal_uart.o(.text)
    HAL_MultiProcessor_Init                  0x0800ce37   Thumb Code   166  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DeInit                          0x0800cedf   Thumb Code    62  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Transmit                        0x0800d015   Thumb Code   190  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Receive                         0x0800d0d3   Thumb Code   194  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Transmit_IT                     0x0800d195   Thumb Code    56  stm32f4xx_hal_uart.o(.text)
    UART_Start_Receive_IT                    0x0800d1cd   Thumb Code    64  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Receive_IT                      0x0800d20d   Thumb Code    44  stm32f4xx_hal_uart.o(.text)
    HAL_UART_ErrorCallback                   0x0800d239   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_TxHalfCpltCallback              0x0800d2b1   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_TxCpltCallback                  0x0800d2c1   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Transmit_DMA                    0x0800d31b   Thumb Code   144  stm32f4xx_hal_uart.o(.text)
    HAL_UART_RxHalfCpltCallback              0x0800d3ab   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_RxCpltCallback                  0x0800d3d3   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    UART_Start_Receive_DMA                   0x0800d489   Thumb Code   202  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Receive_DMA                     0x0800d553   Thumb Code    44  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DMAPause                        0x0800d57f   Thumb Code   166  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DMAResume                       0x0800d625   Thumb Code   170  stm32f4xx_hal_uart.o(.text)
    HAL_UART_DMAStop                         0x0800d6cf   Thumb Code   160  stm32f4xx_hal_uart.o(.text)
    HAL_UARTEx_ReceiveToIdle                 0x0800d76f   Thumb Code   296  stm32f4xx_hal_uart.o(.text)
    HAL_UARTEx_ReceiveToIdle_IT              0x0800d897   Thumb Code   114  stm32f4xx_hal_uart.o(.text)
    HAL_UARTEx_ReceiveToIdle_DMA             0x0800d909   Thumb Code   112  stm32f4xx_hal_uart.o(.text)
    HAL_UARTEx_GetRxEventType                0x0800d979   Thumb Code     6  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Abort                           0x0800d97f   Thumb Code   282  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortTransmit                   0x0800da99   Thumb Code   126  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortReceive                    0x0800db17   Thumb Code   196  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortCpltCallback               0x0800dbdb   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_Abort_IT                        0x0800dc4d   Thumb Code   328  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortTransmitCpltCallback       0x0800dd95   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortTransmit_IT                0x0800ddaf   Thumb Code   140  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortReceiveCpltCallback        0x0800de3b   Thumb Code     2  stm32f4xx_hal_uart.o(.text)
    HAL_UART_AbortReceive_IT                 0x0800de59   Thumb Code   214  stm32f4xx_hal_uart.o(.text)
    HAL_UART_IRQHandler                      0x0800e0cd   Thumb Code   772  stm32f4xx_hal_uart.o(.text)
    HAL_LIN_SendBreak                        0x0800e3d1   Thumb Code    80  stm32f4xx_hal_uart.o(.text)
    HAL_MultiProcessor_EnterMuteMode         0x0800e421   Thumb Code    82  stm32f4xx_hal_uart.o(.text)
    HAL_MultiProcessor_ExitMuteMode          0x0800e473   Thumb Code    82  stm32f4xx_hal_uart.o(.text)
    HAL_HalfDuplex_EnableTransmitter         0x0800e4c5   Thumb Code    68  stm32f4xx_hal_uart.o(.text)
    HAL_HalfDuplex_EnableReceiver            0x0800e509   Thumb Code    68  stm32f4xx_hal_uart.o(.text)
    HAL_UART_GetState                        0x0800e54d   Thumb Code    20  stm32f4xx_hal_uart.o(.text)
    HAL_UART_GetError                        0x0800e561   Thumb Code     6  stm32f4xx_hal_uart.o(.text)
    SystemInit                               0x0800e56d   Thumb Code    14  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x0800e57b   Thumb Code   160  system_stm32f4xx.o(.text)
    rt_ringbuffer_init                       0x0800e677   Thumb Code    84  ringbuffer.o(.text)
    rt_ringbuffer_data_len                   0x0800e6cb   Thumb Code    60  ringbuffer.o(.text)
    rt_ringbuffer_put                        0x0800e707   Thumb Code   182  ringbuffer.o(.text)
    rt_ringbuffer_put_force                  0x0800e7bd   Thumb Code   246  ringbuffer.o(.text)
    rt_ringbuffer_get                        0x0800e8b3   Thumb Code   180  ringbuffer.o(.text)
    rt_ringbuffer_peek                       0x0800e967   Thumb Code   116  ringbuffer.o(.text)
    rt_ringbuffer_putchar                    0x0800e9db   Thumb Code   204  ringbuffer.o(.text)
    rt_ringbuffer_putchar_force              0x0800eaa7   Thumb Code   146  ringbuffer.o(.text)
    rt_ringbuffer_getchar                    0x0800eb39   Thumb Code   108  ringbuffer.o(.text)
    rt_ringbuffer_reset                      0x0800eba5   Thumb Code    60  ringbuffer.o(.text)
    Emm_V5_Reset_CurPos_To_Zero              0x0800ebf9   Thumb Code    54  emm_v5.o(.text)
    Emm_V5_Reset_Clog_Pro                    0x0800ec2f   Thumb Code    54  emm_v5.o(.text)
    Emm_V5_Read_Sys_Params                   0x0800ec65   Thumb Code   274  emm_v5.o(.text)
    Emm_V5_Modify_Ctrl_Mode                  0x0800ed77   Thumb Code    70  emm_v5.o(.text)
    Emm_V5_En_Control                        0x0800edbd   Thumb Code    70  emm_v5.o(.text)
    Emm_V5_Vel_Control                       0x0800ee03   Thumb Code   144  emm_v5.o(.text)
    Emm_V5_Pos_Control                       0x0800ee93   Thumb Code   116  emm_v5.o(.text)
    Emm_V5_Stop_Now                          0x0800ef07   Thumb Code    60  emm_v5.o(.text)
    Emm_V5_Synchronous_motion                0x0800ef43   Thumb Code    54  emm_v5.o(.text)
    Emm_V5_Origin_Set_O                      0x0800ef79   Thumb Code   180  emm_v5.o(.text)
    Emm_V5_Origin_Modify_Params              0x0800f02d   Thumb Code   180  emm_v5.o(.text)
    Emm_V5_Origin_Trigger_Return             0x0800f0e1   Thumb Code    64  emm_v5.o(.text)
    Emm_V5_Origin_Interrupt                  0x0800f121   Thumb Code    54  emm_v5.o(.text)
    Emm_V5_Parse_Response                    0x0800f157   Thumb Code   748  emm_v5.o(.text)
    PID_struct_init                          0x0800f49f   Thumb Code    88  mypid.o(.text)
    PID_INIT                                 0x0800f4f7   Thumb Code   148  mypid.o(.text)
    abs_limit                                0x0800f58b   Thumb Code    46  mypid.o(.text)
    pid_calc                                 0x0800f5b9   Thumb Code   734  mypid.o(.text)
    pid_calc_i_separation                    0x0800f897   Thumb Code   742  mypid.o(.text)
    pid_calc_d                               0x0800fb7d   Thumb Code   412  mypid.o(.text)
    pid_angle_calc                           0x0800fd19   Thumb Code   510  mypid.o(.text)
    pid_yaw_calc                             0x0800ff17   Thumb Code   516  mypid.o(.text)
    pid_clear                                0x0801011b   Thumb Code    34  mypid.o(.text)
    Oled_Printf                              0x08010155   Thumb Code    58  oled_driver.o(.text)
    OLED_SendBuff                            0x0801018f   Thumb Code    52  oled_driver.o(.text)
    ParseRectangleData                       0x0801020d   Thumb Code   238  pi_bsp.o(.text)
    pi_proc                                  0x080102fb   Thumb Code   180  pi_bsp.o(.text)
    GetLatestVisionData                      0x080103af   Thumb Code    36  pi_bsp.o(.text)
    GetVisionDataStats                       0x080103d3   Thumb Code    26  pi_bsp.o(.text)
    IsVisionDataRealtime                     0x080103ed   Thumb Code    28  pi_bsp.o(.text)
    HasNewVisionData                         0x08010409   Thumb Code     6  pi_bsp.o(.text)
    GetCurrentVisionData                     0x0801040f   Thumb Code    36  pi_bsp.o(.text)
    schedule_init                            0x080104a5   Thumb Code     8  schedule.o(.text)
    schedule_run                             0x080104ad   Thumb Code    78  schedule.o(.text)
    Step_Motor_Stop                          0x08010549   Thumb Code    24  step_motor_bsp.o(.text)
    Step_Motor_Init                          0x08010561   Thumb Code    32  step_motor_bsp.o(.text)
    Step_Motor_Set_Speed                     0x08010581   Thumb Code   150  step_motor_bsp.o(.text)
    Step_Motor_Set_Speed_my                  0x08010617   Thumb Code   244  step_motor_bsp.o(.text)
    Step_Motor_Set_Pwm                       0x0801070b   Thumb Code   100  step_motor_bsp.o(.text)
    step_motor_proc                          0x0801076f   Thumb Code     2  step_motor_bsp.o(.text)
    my_printf                                0x080107ed   Thumb Code    58  uart_bsp.o(.text)
    check_motor_angle_limits                 0x08010827   Thumb Code   170  uart_bsp.o(.text)
    calc_motor_angle                         0x080108d1   Thumb Code    42  uart_bsp.o(.text)
    calc_relative_angle                      0x080108fb   Thumb Code    68  uart_bsp.o(.text)
    parse_x_motor_data                       0x0801093f   Thumb Code   276  uart_bsp.o(.text)
    parse_y_motor_data                       0x08010a53   Thumb Code    24  uart_bsp.o(.text)
    process_reset_command                    0x08010a6b   Thumb Code    84  uart_bsp.o(.text)
    save_initial_position                    0x08010abf   Thumb Code    38  uart_bsp.o(.text)
    process_command                          0x08010ae5   Thumb Code    64  uart_bsp.o(.text)
    uart_proc                                0x08010b25   Thumb Code     2  uart_bsp.o(.text)
    Oled_Init                                0x08010c91   Thumb Code    38  oled_app.o(.text)
    Oled_Task                                0x08010cb7   Thumb Code   378  oled_app.o(.text)
    oled_proc                                0x08010e31   Thumb Code     8  oled_app.o(.text)
    key_read                                 0x08010f15   Thumb Code     4  key_bsp.o(.text)
    key_proc                                 0x08010f19   Thumb Code    60  key_bsp.o(.text)
    OLED_WR_CMD                              0x08010fa9   Thumb Code    34  oled.o(.text)
    OLED_Init                                0x08010fcb   Thumb Code    32  oled.o(.text)
    OLED_WR_DATA                             0x08010feb   Thumb Code    34  oled.o(.text)
    OLED_On                                  0x0801100d   Thumb Code    56  oled.o(.text)
    OLED_Clear                               0x08011045   Thumb Code    56  oled.o(.text)
    OLED_Display_On                          0x0801107d   Thumb Code    22  oled.o(.text)
    OLED_Display_Off                         0x08011093   Thumb Code    22  oled.o(.text)
    OLED_Set_Pos                             0x080110a9   Thumb Code    36  oled.o(.text)
    oled_pow                                 0x080110cd   Thumb Code    22  oled.o(.text)
    OLED_ShowChar                            0x080110e3   Thumb Code   218  oled.o(.text)
    OLED_ShowString                          0x080111bd   Thumb Code   104  oled.o(.text)
    OLED_ShowNum                             0x08011225   Thumb Code   148  oled.o(.text)
    OLED_Showdecimal                         0x080112b9   Thumb Code   426  oled.o(.text)
    OLED_ShowCHinese                         0x08011463   Thumb Code   132  oled.o(.text)
    OLED_DrawBMP                             0x080114e7   Thumb Code   144  oled.o(.text)
    OLED_HorizontalShift                     0x08011577   Thumb Code    60  oled.o(.text)
    OLED_Some_HorizontalShift                0x080115b3   Thumb Code    64  oled.o(.text)
    OLED_VerticalAndHorizontalShift          0x080115f3   Thumb Code    66  oled.o(.text)
    OLED_DisplayMode                         0x08011635   Thumb Code    12  oled.o(.text)
    OLED_IntensityControl                    0x08011641   Thumb Code    18  oled.o(.text)
    __aeabi_uldivmod                         0x08011659   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08011659   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08011749   Thumb Code    48  vsnprintf.o(.text)
    __0sscanf                                0x0801177d   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x080117b9   Thumb Code   332  _scanf_int.o(.text)
    atoi                                     0x08011905   Thumb Code    26  atoi.o(.text)
    __aeabi_assert                           0x08011921   Thumb Code    86  assert.o(.text)
    __assert                                 0x08011921   Thumb Code     0  assert.o(.text)
    _strtok_r                                0x080119a1   Thumb Code     4  strtok_r.o(.text)
    strtok_r                                 0x080119a1   Thumb Code     0  strtok_r.o(.text)
    strncmp                                  0x080119a5   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x08011a3b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08011a3b   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08011aa1   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x08011ac5   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08011ac5   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08011ac5   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08011b0d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x08011b29   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08011b29   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08011b29   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08011b2d   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x08011b79   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08011bf9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08011bfb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08011bfd   Thumb Code     2  heapauxi.o(.text)
    __aeabi_errno_addr                       0x08011c01   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08011c01   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08011c01   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __read_errno                             0x08011c09   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08011c13   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x08011c1f   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08011c4b   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08011c6d   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08011c7f   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08011c91   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08011ce5   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08011d5d   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08011d8f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08011db5   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08011dbf   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08011dd1   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08011e8d   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08011f09   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08011f4b   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08011f63   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08011f79   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08011fcf   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08011feb   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08011ff7   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x0801200d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    _chval                                   0x08012195   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x080121bd   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x080121dd   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x080121fb   Thumb Code    34  _sgetc.o(.text)
    __strtod_int                             0x08012259   Thumb Code    90  strtod.o(.text)
    strtol                                   0x080122c1   Thumb Code   112  strtol.o(.text)
    abort                                    0x08012331   Thumb Code    22  abort.o(.text)
    __assert_puts                            0x08012347   Thumb Code    20  assert_puts.o(.text)
    __strtok_internal                        0x0801235d   Thumb Code    64  strtok_int.o(.text)
    _ttywrch                                 0x080123a1   Thumb Code    14  sys_wrch.o(.text)
    _sys_exit                                0x080123b1   Thumb Code     8  sys_exit.o(.text)
    __user_libspace                          0x080123bd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080123bd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080123bd   Thumb Code     0  libspace.o(.text)
    __rt_ctype_table                         0x080123c5   Thumb Code    16  rt_ctype_table.o(.text)
    _ll_udiv10                               0x080123d5   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x0801245f   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08012471   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08012523   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080126d5   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08012941   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08012c3d   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08012c51   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08012c61   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08012c69   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08012c7d   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08012c8d   Thumb Code     8  _printf_wchar.o(.text)
    __vfscanf                                0x08012c95   Thumb Code   880  _scanf.o(.text)
    _strtoul                                 0x08013009   Thumb Code   158  _strtoul.o(.text)
    _wcrtomb                                 0x080130a7   Thumb Code    64  _wcrtomb.o(.text)
    __rt_SIGABRT                             0x080130e7   Thumb Code    14  defsig_abrt_outer.o(.text)
    strcspn                                  0x080130f5   Thumb Code    32  strcspn.o(.text)
    strspn                                   0x08013115   Thumb Code    28  strspn.o(.text)
    __I$use$semihosting                      0x08013131   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08013131   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08013133   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x08013133   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_locale                              0x0801317d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08013185   Thumb Code   112  _printf_fp_infnan.o(.text)
    _scanf_really_real                       0x08013451   Thumb Code   684  scanf_fp.o(.text)
    _btod_etento                             0x080136fd   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x080137e1   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x080137f3   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x080137fd   Thumb Code    14  defsig_abrt_inner.o(.text)
    __default_signal_display                 0x0801382d   Thumb Code    50  defsig_general.o(.text)
    _scanf_really_hex_real                   0x08013861   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x08013b81   Thumb Code   292  scanf_infnan.o(.text)
    __aeabi_llsl                             0x08013cb5   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x08013cb5   Thumb Code    38  llshl.o(.text)
    _btod_d2e                                0x08013cdb   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08013d19   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08013d5f   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08013dbf   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x080140f9   Thumb Code   122  btod.o(CL$$btod_e2d)
    _e2e                                     0x0801417d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08014259   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x08014283   Thumb Code    42  btod.o(CL$$btod_edivd)
    _btod_emul                               0x080142ad   Thumb Code    42  btod.o(CL$$btod_emul)
    _btod_emuld                              0x080142d7   Thumb Code    42  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x08014301   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08014545   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp___mathlib_tofloat               0x08014579   Thumb Code   232  narrow.o(i.__hardfp___mathlib_tofloat)
    __hardfp_atof                            0x08014671   Thumb Code    44  atof.o(i.__hardfp_atof)
    __hardfp_fabs                            0x080146a9   Thumb Code    20  fabs.o(i.__hardfp_fabs)
    __hardfp_fmod                            0x080146bd   Thumb Code   254  fmod.o(i.__hardfp_fmod)
    __hardfp_ldexp                           0x080147c1   Thumb Code   200  ldexp.o(i.__hardfp_ldexp)
    __mathlib_dbl_invalid                    0x08014891   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x080148b1   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x080148d1   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_narrow                         0x080148f1   Thumb Code    18  narrow.o(i.__mathlib_narrow)
    __support_ldexp                          0x08014903   Thumb Code    20  ldexp.o(i.__support_ldexp)
    _is_digit                                0x08014917   Thumb Code    14  __printf_wp.o(i._is_digit)
    frexp                                    0x08014929   Thumb Code   118  frexp.o(i.frexp)
    _get_lc_numeric                          0x080149b5   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080149e1   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08014a0d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08014a0d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08014a71   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08014a71   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08014bc1   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x08014bd1   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08014be9   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08014be9   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x08014e99   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08014e99   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_cdcmple                          0x08014f11   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08014f11   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08014f73   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08014f89   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08014f89   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080150dd   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    _drem                                    0x08015179   Thumb Code   328  drem_clz.o(x$fpl$drem)
    __fpl_dretinf                            0x080152c9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x080152d5   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x080152d5   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08015341   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08015341   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x08015359   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08015359   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0801552d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0801552d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08015583   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0801560f   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08015617   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08015617   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08015619   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __ieee_status                            0x08015623   Thumb Code     6  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x08015629   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0801562d   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x08015631   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08015695   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x080156f1   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x080156f5   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x080156f9   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x080156fd   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x0801572c   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08015734   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08015744   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x08015908   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08015928   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x08015928   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x08015928   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x08015928   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x08015928   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x08015928   Data           0  fpconst.o(c$$dinf)
    __dbl_max                                0x08015930   Data           0  fpconst.o(c$$dmax)
    __ctype                                  0x08015961   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000000   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    vision_data_updated                      0x20000010   Data           1  pi_bsp.o(.data)
    task_num                                 0x20000024   Data           1  schedule.o(.data)
    x_motor_angle                            0x20000058   Data           4  uart_bsp.o(.data)
    y_motor_angle                            0x2000005c   Data           4  uart_bsp.o(.data)
    x_angle_limit_flag                       0x20000060   Data           1  uart_bsp.o(.data)
    y_angle_limit_flag                       0x20000061   Data           1  uart_bsp.o(.data)
    motor_angle_limit_check_enabled          0x20000062   Data           1  uart_bsp.o(.data)
    x_reference_position                     0x20000064   Data           4  uart_bsp.o(.data)
    y_reference_position                     0x20000068   Data           4  uart_bsp.o(.data)
    x_reference_initialized                  0x2000006c   Data           1  uart_bsp.o(.data)
    y_reference_initialized                  0x2000006d   Data           1  uart_bsp.o(.data)
    x_relative_angle                         0x20000070   Data           4  uart_bsp.o(.data)
    y_relative_angle                         0x20000074   Data           4  uart_bsp.o(.data)
    x_initial_position                       0x20000078   Data           4  uart_bsp.o(.data)
    y_initial_position                       0x2000007c   Data           4  uart_bsp.o(.data)
    x_initial_direction                      0x20000080   Data           1  uart_bsp.o(.data)
    y_initial_direction                      0x20000081   Data           1  uart_bsp.o(.data)
    initial_position_saved                   0x20000082   Data           1  uart_bsp.o(.data)
    key_val                                  0x2000008c   Data           1  key_bsp.o(.data)
    key_old                                  0x2000008d   Data           1  key_bsp.o(.data)
    key_down                                 0x2000008e   Data           1  key_bsp.o(.data)
    key_up                                   0x2000008f   Data           1  key_bsp.o(.data)
    CMD_Data                                 0x20000918   Data          23  oled.o(.data)
    hi2c3                                    0x20000930   Data          84  i2c.o(.bss)
    htim1                                    0x20000984   Data          72  tim.o(.bss)
    htim3                                    0x200009cc   Data          72  tim.o(.bss)
    htim4                                    0x20000a14   Data          72  tim.o(.bss)
    motor_x_buf                              0x20000a5c   Data          64  usart.o(.bss)
    motor_y_buf                              0x20000a9c   Data          64  usart.o(.bss)
    pi_rx_buf                                0x20000adc   Data         128  usart.o(.bss)
    user_rx_buf                              0x20000b5c   Data          64  usart.o(.bss)
    uart3_rx_buffer                          0x20000b9c   Data          32  usart.o(.bss)
    huart4                                   0x20000bbc   Data          72  usart.o(.bss)
    huart5                                   0x20000c04   Data          72  usart.o(.bss)
    huart1                                   0x20000c4c   Data          72  usart.o(.bss)
    huart2                                   0x20000c94   Data          72  usart.o(.bss)
    huart3                                   0x20000cdc   Data          72  usart.o(.bss)
    huart6                                   0x20000d24   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x20000d6c   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x20000dcc   Data          96  usart.o(.bss)
    hdma_usart3_rx                           0x20000e2c   Data          96  usart.o(.bss)
    hdma_usart6_rx                           0x20000e8c   Data          96  usart.o(.bss)
    pid_x                                    0x20000eec   Data          80  mypid.o(.bss)
    pid_y                                    0x20000f3c   Data          80  mypid.o(.bss)
    pid_speed_left                           0x20000f8c   Data          80  mypid.o(.bss)
    pid_speed_right                          0x20000fdc   Data          80  mypid.o(.bss)
    pid_location_left                        0x2000102c   Data          80  mypid.o(.bss)
    pid_location_right                       0x2000107c   Data          80  mypid.o(.bss)
    latest_vision_data                       0x200010cc   Data         124  pi_bsp.o(.bss)
    ringbuffer_x                             0x20001348   Data          12  uart_bsp.o(.bss)
    ringbuffer_y                             0x20001354   Data          12  uart_bsp.o(.bss)
    ringbuffer_pi                            0x20001360   Data          12  uart_bsp.o(.bss)
    ringbuffer_pool_x                        0x2000136c   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_y                        0x200013ac   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_pi                       0x200013ec   Data         256  uart_bsp.o(.bss)
    output_buffer_x                          0x200014ec   Data          64  uart_bsp.o(.bss)
    output_buffer_y                          0x2000152c   Data          64  uart_bsp.o(.bss)
    output_buffer_pi                         0x2000156c   Data          64  uart_bsp.o(.bss)
    __libspace_start                         0x200015ac   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000160c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00016394, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x00016018])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00015a64, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1354  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1857    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         1855    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         1859    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1483    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         1472    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000242   0x08000242   0x00000006   Code   RO         1474    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000248   0x08000248   0x00000006   Code   RO         1479    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         1480    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000254   0x08000254   0x00000006   Code   RO         1481    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         1482    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000260   0x08000260   0x0000000a   Code   RO         1487    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         1476    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000270   0x08000270   0x00000006   Code   RO         1477    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000276   0x08000276   0x00000006   Code   RO         1478    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         1475    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000282   0x08000282   0x00000006   Code   RO         1473    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000288   0x08000288   0x00000006   Code   RO         1484    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         1485    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000294   0x08000294   0x00000006   Code   RO         1486    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         1491    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         1492    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002a6   0x080002a6   0x0000000a   Code   RO         1488    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002b0   0x080002b0   0x00000006   Code   RO         1470    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002b6   0x080002b6   0x00000006   Code   RO         1471    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002bc   0x080002bc   0x00000006   Code   RO         1489    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002c2   0x080002c2   0x00000006   Code   RO         1490    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         1593    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002cc   0x080002cc   0x00000002   Code   RO         1678    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002ce   0x080002ce   0x00000004   Code   RO         1679    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         1682    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         1685    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         1687    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         1689    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000006   Code   RO         1690    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002d8   0x080002d8   0x00000000   Code   RO         1692    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002d8   0x080002d8   0x0000000c   Code   RO         1693    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x00000000   Code   RO         1694    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x00000000   Code   RO         1696    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x0000000a   Code   RO         1697    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1698    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1700    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1702    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1704    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1706    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1708    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1710    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1712    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1716    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1718    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1720    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         1722    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000002   Code   RO         1723    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002f0   0x080002f0   0x00000002   Code   RO         1809    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         1827    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         1829    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         1831    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         1834    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         1837    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         1839    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         1842    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000002   Code   RO         1843    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO         1388    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO         1558    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002f4   0x080002f4   0x00000006   Code   RO         1570    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         1560    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002fa   0x080002fa   0x00000004   Code   RO         1561    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         1563    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002fe   0x080002fe   0x00000008   Code   RO         1564    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000306   0x08000306   0x00000002   Code   RO         1728    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000308   0x08000308   0x00000000   Code   RO         1759    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000308   0x08000308   0x00000004   Code   RO         1760    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800030c   0x0800030c   0x00000006   Code   RO         1761    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000312   0x08000312   0x00000002   PAD
    0x08000314   0x08000314   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000354   0x08000354   0x000001c4   Code   RO           13    .text               main.o
    0x08000518   0x08000518   0x000000c0   Code   RO          235    .text               gpio.o
    0x080005d8   0x080005d8   0x00000088   Code   RO          259    .text               dma.o
    0x08000660   0x08000660   0x00000130   Code   RO          283    .text               i2c.o
    0x08000790   0x08000790   0x000003b8   Code   RO          313    .text               tim.o
    0x08000b48   0x08000b48   0x00000760   Code   RO          343    .text               usart.o
    0x080012a8   0x080012a8   0x00000140   Code   RO          377    .text               stm32f4xx_it.o
    0x080013e8   0x080013e8   0x00000048   Code   RO          405    .text               stm32f4xx_hal_msp.o
    0x08001430   0x08001430   0x00004670   Code   RO          429    .text               stm32f4xx_hal_i2c.o
    0x08005aa0   0x08005aa0   0x00000948   Code   RO          471    .text               stm32f4xx_hal_rcc.o
    0x080063e8   0x080063e8   0x000003b4   Code   RO          589    .text               stm32f4xx_hal_gpio.o
    0x0800679c   0x0800679c   0x000008b4   Code   RO          637    .text               stm32f4xx_hal_dma.o
    0x08007050   0x08007050   0x000003d0   Code   RO          710    .text               stm32f4xx_hal_cortex.o
    0x08007420   0x08007420   0x00000210   Code   RO          738    .text               stm32f4xx_hal.o
    0x08007630   0x08007630   0x00003c8c   Code   RO          792    .text               stm32f4xx_hal_tim.o
    0x0800b2bc   0x0800b2bc   0x000017d4   Code   RO          816    .text               stm32f4xx_hal_tim_ex.o
    0x0800ca90   0x0800ca90   0x00001adc   Code   RO          840    .text               stm32f4xx_hal_uart.o
    0x0800e56c   0x0800e56c   0x000000c8   Code   RO          864    .text               system_stm32f4xx.o
    0x0800e634   0x0800e634   0x000005ac   Code   RO          892    .text               ringbuffer.o
    0x0800ebe0   0x0800ebe0   0x00000862   Code   RO          917    .text               emm_v5.o
    0x0800f442   0x0800f442   0x00000002   PAD
    0x0800f444   0x0800f444   0x00000cf8   Code   RO          995    .text               mypid.o
    0x0801013c   0x0801013c   0x000000b8   Code   RO         1027    .text               oled_driver.o
    0x080101f4   0x080101f4   0x00000298   Code   RO         1125    .text               pi_bsp.o
    0x0801048c   0x0801048c   0x000000a4   Code   RO         1153    .text               schedule.o
    0x08010530   0x08010530   0x000002a4   Code   RO         1180    .text               step_motor_bsp.o
    0x080107d4   0x080107d4   0x000004a4   Code   RO         1204    .text               uart_bsp.o
    0x08010c78   0x08010c78   0x00000284   Code   RO         1236    .text               oled_app.o
    0x08010efc   0x08010efc   0x00000094   Code   RO         1262    .text               key_bsp.o
    0x08010f90   0x08010f90   0x000006c8   Code   RO         1289    .text               oled.o
    0x08011658   0x08011658   0x000000ee   Code   RO         1328    .text               c_w.l(lludivv7m.o)
    0x08011746   0x08011746   0x00000002   PAD
    0x08011748   0x08011748   0x00000034   Code   RO         1330    .text               c_w.l(vsnprintf.o)
    0x0801177c   0x0801177c   0x0000003c   Code   RO         1332    .text               c_w.l(__0sscanf.o)
    0x080117b8   0x080117b8   0x0000014c   Code   RO         1334    .text               c_w.l(_scanf_int.o)
    0x08011904   0x08011904   0x0000001a   Code   RO         1336    .text               c_w.l(atoi.o)
    0x0801191e   0x0801191e   0x00000002   PAD
    0x08011920   0x08011920   0x00000080   Code   RO         1338    .text               c_w.l(assert.o)
    0x080119a0   0x080119a0   0x00000004   Code   RO         1340    .text               c_w.l(strtok_r.o)
    0x080119a4   0x080119a4   0x00000096   Code   RO         1342    .text               c_w.l(strncmp.o)
    0x08011a3a   0x08011a3a   0x0000008a   Code   RO         1344    .text               c_w.l(rt_memcpy_v6.o)
    0x08011ac4   0x08011ac4   0x00000064   Code   RO         1346    .text               c_w.l(rt_memcpy_w.o)
    0x08011b28   0x08011b28   0x0000004e   Code   RO         1348    .text               c_w.l(rt_memclr_w.o)
    0x08011b76   0x08011b76   0x00000002   PAD
    0x08011b78   0x08011b78   0x00000080   Code   RO         1350    .text               c_w.l(strcmpv7m.o)
    0x08011bf8   0x08011bf8   0x00000006   Code   RO         1352    .text               c_w.l(heapauxi.o)
    0x08011bfe   0x08011bfe   0x00000002   PAD
    0x08011c00   0x08011c00   0x00000008   Code   RO         1396    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08011c08   0x08011c08   0x00000016   Code   RO         1398    .text               c_w.l(_rserrno.o)
    0x08011c1e   0x08011c1e   0x0000004e   Code   RO         1402    .text               c_w.l(_printf_pad.o)
    0x08011c6c   0x08011c6c   0x00000024   Code   RO         1404    .text               c_w.l(_printf_truncate.o)
    0x08011c90   0x08011c90   0x00000052   Code   RO         1406    .text               c_w.l(_printf_str.o)
    0x08011ce2   0x08011ce2   0x00000002   PAD
    0x08011ce4   0x08011ce4   0x00000078   Code   RO         1408    .text               c_w.l(_printf_dec.o)
    0x08011d5c   0x08011d5c   0x00000028   Code   RO         1410    .text               c_w.l(_printf_charcount.o)
    0x08011d84   0x08011d84   0x00000030   Code   RO         1412    .text               c_w.l(_printf_char_common.o)
    0x08011db4   0x08011db4   0x0000000a   Code   RO         1414    .text               c_w.l(_sputc.o)
    0x08011dbe   0x08011dbe   0x00000010   Code   RO         1416    .text               c_w.l(_snputc.o)
    0x08011dce   0x08011dce   0x00000002   PAD
    0x08011dd0   0x08011dd0   0x000000bc   Code   RO         1418    .text               c_w.l(_printf_wctomb.o)
    0x08011e8c   0x08011e8c   0x0000007c   Code   RO         1421    .text               c_w.l(_printf_longlong_dec.o)
    0x08011f08   0x08011f08   0x00000070   Code   RO         1427    .text               c_w.l(_printf_oct_int_ll.o)
    0x08011f78   0x08011f78   0x00000094   Code   RO         1447    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x0801200c   0x0801200c   0x00000188   Code   RO         1467    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08012194   0x08012194   0x0000001c   Code   RO         1493    .text               c_w.l(_chval.o)
    0x080121b0   0x080121b0   0x0000002c   Code   RO         1495    .text               c_w.l(scanf_char.o)
    0x080121dc   0x080121dc   0x00000040   Code   RO         1497    .text               c_w.l(_sgetc.o)
    0x0801221c   0x0801221c   0x000000a4   Code   RO         1499    .text               c_w.l(strtod.o)
    0x080122c0   0x080122c0   0x00000070   Code   RO         1501    .text               c_w.l(strtol.o)
    0x08012330   0x08012330   0x00000016   Code   RO         1503    .text               c_w.l(abort.o)
    0x08012346   0x08012346   0x00000014   Code   RO         1505    .text               c_w.l(assert_puts.o)
    0x0801235a   0x0801235a   0x00000002   PAD
    0x0801235c   0x0801235c   0x00000044   Code   RO         1507    .text               c_w.l(strtok_int.o)
    0x080123a0   0x080123a0   0x0000000e   Code   RO         1550    .text               c_w.l(sys_wrch.o)
    0x080123ae   0x080123ae   0x00000002   PAD
    0x080123b0   0x080123b0   0x0000000c   Code   RO         1552    .text               c_w.l(sys_exit.o)
    0x080123bc   0x080123bc   0x00000008   Code   RO         1554    .text               c_w.l(libspace.o)
    0x080123c4   0x080123c4   0x00000010   Code   RO         1572    .text               c_w.l(rt_ctype_table.o)
    0x080123d4   0x080123d4   0x0000008a   Code   RO         1576    .text               c_w.l(lludiv10.o)
    0x0801245e   0x0801245e   0x00000012   Code   RO         1578    .text               c_w.l(isspace.o)
    0x08012470   0x08012470   0x000000b2   Code   RO         1580    .text               c_w.l(_printf_intcommon.o)
    0x08012522   0x08012522   0x0000041e   Code   RO         1582    .text               c_w.l(_printf_fp_dec.o)
    0x08012940   0x08012940   0x000002fc   Code   RO         1584    .text               c_w.l(_printf_fp_hex.o)
    0x08012c3c   0x08012c3c   0x0000002c   Code   RO         1589    .text               c_w.l(_printf_char.o)
    0x08012c68   0x08012c68   0x0000002c   Code   RO         1591    .text               c_w.l(_printf_wchar.o)
    0x08012c94   0x08012c94   0x00000374   Code   RO         1594    .text               c_w.l(_scanf.o)
    0x08013008   0x08013008   0x0000009e   Code   RO         1596    .text               c_w.l(_strtoul.o)
    0x080130a6   0x080130a6   0x00000040   Code   RO         1598    .text               c_w.l(_wcrtomb.o)
    0x080130e6   0x080130e6   0x0000000e   Code   RO         1603    .text               c_w.l(defsig_abrt_outer.o)
    0x080130f4   0x080130f4   0x00000020   Code   RO         1607    .text               c_w.l(strcspn.o)
    0x08013114   0x08013114   0x0000001c   Code   RO         1609    .text               c_w.l(strspn.o)
    0x08013130   0x08013130   0x00000002   Code   RO         1619    .text               c_w.l(use_no_semi.o)
    0x08013132   0x08013132   0x00000000   Code   RO         1621    .text               c_w.l(indicate_semi.o)
    0x08013132   0x08013132   0x0000004a   Code   RO         1622    .text               c_w.l(sys_stackheap_outer.o)
    0x0801317c   0x0801317c   0x00000008   Code   RO         1629    .text               c_w.l(rt_locale_intlibspace.o)
    0x08013184   0x08013184   0x00000080   Code   RO         1631    .text               c_w.l(_printf_fp_infnan.o)
    0x08013204   0x08013204   0x000004f8   Code   RO         1633    .text               c_w.l(scanf_fp.o)
    0x080136fc   0x080136fc   0x000000e4   Code   RO         1635    .text               c_w.l(bigflt0.o)
    0x080137e0   0x080137e0   0x00000012   Code   RO         1663    .text               c_w.l(exit.o)
    0x080137f2   0x080137f2   0x0000000a   Code   RO         1665    .text               c_w.l(defsig_exit.o)
    0x080137fc   0x080137fc   0x00000030   Code   RO         1667    .text               c_w.l(defsig_abrt_inner.o)
    0x0801382c   0x0801382c   0x00000032   Code   RO         1732    .text               c_w.l(defsig_general.o)
    0x0801385e   0x0801385e   0x00000002   PAD
    0x08013860   0x08013860   0x00000320   Code   RO         1765    .text               c_w.l(scanf_hexfp.o)
    0x08013b80   0x08013b80   0x00000134   Code   RO         1767    .text               c_w.l(scanf_infnan.o)
    0x08013cb4   0x08013cb4   0x00000026   Code   RO         1787    .text               c_w.l(llshl.o)
    0x08013cda   0x08013cda   0x0000003e   Code   RO         1638    CL$$btod_d2e        c_w.l(btod.o)
    0x08013d18   0x08013d18   0x00000046   Code   RO         1640    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08013d5e   0x08013d5e   0x00000060   Code   RO         1639    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08013dbe   0x08013dbe   0x00000338   Code   RO         1648    CL$$btod_div_common  c_w.l(btod.o)
    0x080140f6   0x080140f6   0x00000002   PAD
    0x080140f8   0x080140f8   0x00000084   Code   RO         1646    CL$$btod_e2d        c_w.l(btod.o)
    0x0801417c   0x0801417c   0x000000dc   Code   RO         1645    CL$$btod_e2e        c_w.l(btod.o)
    0x08014258   0x08014258   0x0000002a   Code   RO         1642    CL$$btod_ediv       c_w.l(btod.o)
    0x08014282   0x08014282   0x0000002a   Code   RO         1644    CL$$btod_edivd      c_w.l(btod.o)
    0x080142ac   0x080142ac   0x0000002a   Code   RO         1641    CL$$btod_emul       c_w.l(btod.o)
    0x080142d6   0x080142d6   0x0000002a   Code   RO         1643    CL$$btod_emuld      c_w.l(btod.o)
    0x08014300   0x08014300   0x00000244   Code   RO         1647    CL$$btod_mult_common  c_w.l(btod.o)
    0x08014544   0x08014544   0x00000030   Code   RO         1726    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08014574   0x08014574   0x00000004   PAD
    0x08014578   0x08014578   0x000000f8   Code   RO         1750    i.__hardfp___mathlib_tofloat  m_wm.l(narrow.o)
    0x08014670   0x08014670   0x00000038   Code   RO         1364    i.__hardfp_atof     m_wm.l(atof.o)
    0x080146a8   0x080146a8   0x00000014   Code   RO         1370    i.__hardfp_fabs     m_wm.l(fabs.o)
    0x080146bc   0x080146bc   0x00000104   Code   RO         1376    i.__hardfp_fmod     m_wm.l(fmod.o)
    0x080147c0   0x080147c0   0x000000d0   Code   RO         1810    i.__hardfp_ldexp    m_wm.l(ldexp.o)
    0x08014890   0x08014890   0x00000020   Code   RO         1539    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x080148b0   0x080148b0   0x00000020   Code   RO         1540    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x080148d0   0x080148d0   0x00000020   Code   RO         1542    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x080148f0   0x080148f0   0x00000012   Code   RO         1751    i.__mathlib_narrow  m_wm.l(narrow.o)
    0x08014902   0x08014902   0x00000014   Code   RO         1812    i.__support_ldexp   m_wm.l(ldexp.o)
    0x08014916   0x08014916   0x0000000e   Code   RO         1460    i._is_digit         c_w.l(__printf_wp.o)
    0x08014924   0x08014924   0x00000004   PAD
    0x08014928   0x08014928   0x0000008c   Code   RO         1779    i.frexp             m_wm.l(frexp.o)
    0x080149b4   0x080149b4   0x0000002c   Code   RO         1601    locale$$code        c_w.l(lc_numeric_c.o)
    0x080149e0   0x080149e0   0x0000002c   Code   RO         1661    locale$$code        c_w.l(lc_ctype_c.o)
    0x08014a0c   0x08014a0c   0x00000062   Code   RO         1356    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08014a6e   0x08014a6e   0x00000002   PAD
    0x08014a70   0x08014a70   0x00000150   Code   RO         1509    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08014bc0   0x08014bc0   0x00000010   Code   RO         1847    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x08014bd0   0x08014bd0   0x00000018   Code   RO         1515    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08014be8   0x08014be8   0x000002b0   Code   RO         1612    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08014e98   0x08014e98   0x00000078   Code   RO         1775    x$fpl$deqf          fz_wm.l(deqf.o)
    0x08014f10   0x08014f10   0x00000078   Code   RO         1358    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08014f88   0x08014f88   0x00000154   Code   RO         1615    x$fpl$dmul          fz_wm.l(dmul.o)
    0x080150dc   0x080150dc   0x0000009c   Code   RO         1517    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08015178   0x08015178   0x00000150   Code   RO         1519    x$fpl$drem          fz_wm.l(drem_clz.o)
    0x080152c8   0x080152c8   0x0000000c   Code   RO         1521    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x080152d4   0x080152d4   0x0000006c   Code   RO         1360    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08015340   0x08015340   0x00000016   Code   RO         1510    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08015356   0x08015356   0x00000002   PAD
    0x08015358   0x08015358   0x000001d4   Code   RO         1511    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0801552c   0x0801552c   0x00000056   Code   RO         1362    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08015582   0x08015582   0x0000008c   Code   RO         1523    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0801560e   0x0801560e   0x0000000a   Code   RO         1742    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08015618   0x08015618   0x0000000a   Code   RO         1525    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08015622   0x08015622   0x00000006   Code   RO         1724    x$fpl$ieeestatus    fz_wm.l(istatus.o)
    0x08015628   0x08015628   0x00000004   Code   RO         1527    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0801562c   0x0801562c   0x00000004   Code   RO         1529    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08015630   0x08015630   0x00000064   Code   RO         1849    x$fpl$retnan        fz_wm.l(retnan.o)
    0x08015694   0x08015694   0x0000005c   Code   RO         1844    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x080156f0   0x080156f0   0x00000004   Code   RO         1617    x$fpl$scanf1        fz_wm.l(scanf1.o)
    0x080156f4   0x080156f4   0x00000008   Code   RO         1744    x$fpl$scanf2        fz_wm.l(scanf2.o)
    0x080156fc   0x080156fc   0x00000030   Code   RO         1851    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x0801572c   0x0801572c   0x00000000   Code   RO         1535    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0801572c   0x0801572c   0x00000008   Data   RO          638    .constdata          stm32f4xx_hal_dma.o
    0x08015734   0x08015734   0x00000018   Data   RO          865    .constdata          system_stm32f4xx.o
    0x0801574c   0x0801574c   0x000000c0   Data   RO         1290    .constdata          oled.o
    0x0801580c   0x0801580c   0x00000008   Data   RO         1419    .constdata          c_w.l(_printf_wctomb.o)
    0x08015814   0x08015814   0x00000028   Data   RO         1448    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0801583c   0x0801583c   0x00000011   Data   RO         1468    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0801584d   0x0801584d   0x00000026   Data   RO         1585    .constdata          c_w.l(_printf_fp_hex.o)
    0x08015873   0x08015873   0x00000001   PAD
    0x08015874   0x08015874   0x00000094   Data   RO         1636    .constdata          c_w.l(bigflt0.o)
    0x08015908   0x08015908   0x00000020   Data   RO         1853    Region$$Table       anon$$obj.o
    0x08015928   0x08015928   0x00000008   Data   RO         1738    c$$dinf             fz_wm.l(fpconst.o)
    0x08015930   0x08015930   0x00000008   Data   RO         1741    c$$dmax             fz_wm.l(fpconst.o)
    0x08015938   0x08015938   0x0000001c   Data   RO         1600    locale$$data        c_w.l(lc_numeric_c.o)
    0x08015954   0x08015954   0x00000110   Data   RO         1660    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08015a64, Size: 0x00001c10, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x000005b4])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000009   Data   RW          739    .data               stm32f4xx_hal.o
    0x20000009   COMPRESSED   0x00000003   PAD
    0x2000000c   COMPRESSED   0x00000004   Data   RW          866    .data               system_stm32f4xx.o
    0x20000010   COMPRESSED   0x00000012   Data   RW         1127    .data               pi_bsp.o
    0x20000022   COMPRESSED   0x00000002   PAD
    0x20000024   COMPRESSED   0x00000034   Data   RW         1154    .data               schedule.o
    0x20000058   COMPRESSED   0x0000002b   Data   RW         1206    .data               uart_bsp.o
    0x20000083   COMPRESSED   0x00000001   PAD
    0x20000084   COMPRESSED   0x00000008   Data   RW         1237    .data               oled_app.o
    0x2000008c   COMPRESSED   0x00000004   Data   RW         1263    .data               key_bsp.o
    0x20000090   COMPRESSED   0x0000089f   Data   RW         1291    .data               oled.o
    0x2000092f   COMPRESSED   0x00000001   PAD
    0x20000930        -       0x00000054   Zero   RW          284    .bss                i2c.o
    0x20000984        -       0x000000d8   Zero   RW          314    .bss                tim.o
    0x20000a5c        -       0x00000490   Zero   RW          344    .bss                usart.o
    0x20000eec        -       0x000001e0   Zero   RW          996    .bss                mypid.o
    0x200010cc        -       0x0000027c   Zero   RW         1126    .bss                pi_bsp.o
    0x20001348        -       0x00000264   Zero   RW         1205    .bss                uart_bsp.o
    0x200015ac        -       0x00000060   Zero   RW         1555    .bss                c_w.l(libspace.o)
    0x2000160c   COMPRESSED   0x00000004   PAD
    0x20001610        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20001810        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08016018, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       136          6          0          0          0        810   dma.o
      2146        180          0          0          0       8336   emm_v5.o
         0          0          0          0          0        388   encoder_bsp.o
         0          0          0          0          0       1244   encoder_drv.o
       192          6          0          0          0        951   gpio.o
       304         26          0          0         84       1549   i2c.o
       148         60          0          4          0       1206   key_bsp.o
       452        100          0          0          0     710708   main.o
      3320        134          0          0        480       7919   mypid.o
      1736         66        192       2207          0       7750   oled.o
       644        196          0          8          0       1508   oled_app.o
       184         50          0          0          0       1848   oled_driver.o
       664         90          0         18        636       3652   pi_bsp.o
      1452         96          0          0          0       6639   ringbuffer.o
       164         54          0         52          0       1560   schedule.o
        64         26        392          0       1536        836   startup_stm32f407xx.o
       676        100          0          0          0       2406   step_motor_bsp.o
       528         54          0          9          0      10772   stm32f4xx_hal.o
       976         20          0          0          0      36803   stm32f4xx_hal_cortex.o
      2228         28          8          0          0       7137   stm32f4xx_hal_dma.o
       948         46          0          0          0       3351   stm32f4xx_hal_gpio.o
     18032        196          0          0          0      39678   stm32f4xx_hal_i2c.o
        72          4          0          0          0        842   stm32f4xx_hal_msp.o
      2376         70          0          0          0       5403   stm32f4xx_hal_rcc.o
     15500        382          0          0          0      42562   stm32f4xx_hal_tim.o
      6100        200          0          0          0      17259   stm32f4xx_hal_tim_ex.o
      6876         50          0          0          0      22852   stm32f4xx_hal_uart.o
       320         94          0          0          0       2827   stm32f4xx_it.o
       200         26         24          4          0       1549   system_stm32f4xx.o
       952         42          0          0        216       3455   tim.o
      1188        338          0         43        612       5147   uart_bsp.o
      1888        208          0          0       1168       4526   usart.o

    ----------------------------------------------------------------------
     70468       <USER>        <GROUP>       2352       4732     963473   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          7          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
       128         42          0          0          0         76   assert.o
        20          0          0          0          0         76   assert_puts.o
        26          0          0          0          0         80   atoi.o
       228          4        148          0          0         96   bigflt0.o
      2152        136          0          0          0        960   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
      1272         16          0          0          0        168   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
       128          0          0          0          0         68   strcmpv7m.o
        32          0          0          0          0         80   strcspn.o
       150          0          0          0          0         80   strncmp.o
        28          0          0          0          0         80   strspn.o
       164         14          0          0          0        120   strtod.o
        68          4          0          0          0         84   strtok_int.o
         4          0          0          0          0         68   strtok_r.o
       112          0          0          0          0         88   strtol.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
       336         12          0          0          0        136   drem_clz.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
         0          0         16          0          0          0   fpconst.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         6          0          0          0          0        116   istatus.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
         4          0          0          0          0        116   scanf1.o
         8          0          0          0          0        132   scanf2.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
        56         12          0          0          0        132   atof.o
        96         24          0          0          0        372   dunder.o
        20          0          0          0          0        124   fabs.o
       260          6          0          0          0        144   fmod.o
        48          0          0          0          0        124   fpclassify.o
       140         22          0          0          0        132   frexp.o
       228          8          0          0          0        308   ldexp.o
       266         16          0          0          0        308   narrow.o

    ----------------------------------------------------------------------
     16992        <USER>        <GROUP>          0        100      12324   Library Totals
        36          2          1          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

     12486        438        551          0         96       7168   c_w.l
      3356        208         16          0          0       3512   fz_wm.l
      1114         88          0          0          0       1644   m_wm.l

    ----------------------------------------------------------------------
     16992        <USER>        <GROUP>          0        100      12324   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     87460       3684       1216       2352       4832     962241   Grand Totals
     87460       3684       1216       1460       4832     962241   ELF Image Totals (compressed)
     87460       3684       1216       1460          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                88676 (  86.60kB)
    Total RW  Size (RW Data + ZI Data)              7184 (   7.02kB)
    Total ROM Size (Code + RO Data + RW Data)      90136 (  88.02kB)

==============================================================================

