#include "oled_app.h"

//extern Encoder left_encoder;
//extern Encoder right_encoder;

//extern MOTOR left_motor;
//extern MOTOR right_motor;

//extern PID_T pid_speed_left;  // �����ٶȻ� PID ������
//extern PID_T pid_speed_right; // �����ٶȻ� PID ������

//extern PID_T pid_angle; // ǰ���ǶȻ�

//extern PID_T pid_line; // ѭ��λ��ʽ PID ������(ѭ����)

extern float pitch, roll, yaw; // pitch-�����ǣ���-��+����roll����ǣ�ǰ-��+����yaw-����ǣ���+��-����Ҫ���������ǵ�xyz�ῴ

extern unsigned char Digtal; // �Ҷȴ�����������
extern float g_line_position_error; // ѭ�����ֵ��ѭ������ǰֵ��

extern unsigned char system_mode; // ϵͳģʽ��1~4 ��Ӧ�ĵ���Ŀ

extern unsigned char gray_ff_count; // ��Ȧ����Ȧ������

// 外部变量声明，用于访问pi_bsp.c中的静态变量
extern RectangleData_t latest_vision_data;
extern uint8_t vision_data_updated;

void Oled_Init(void)
{
  OLED_Init();
  OLED_Clear();

  // 显示初始化信息
  OLED_ShowString(0, 0, "OLED Init OK", 12, 0);
  HAL_Delay(1000); // 显示1秒
  OLED_Clear();
}

void Oled_Task(void)
{
    static uint32_t last_update = 0;
    static uint32_t frame_counter = 0; // 帧计数器
    uint32_t current_time = HAL_GetTick();

    // 每50ms更新一次显示，提高实时性
    if(current_time - last_update >= 50) {
        last_update = current_time;
        frame_counter++;

        // 清屏以避免显示重叠
//        OLED_Clear();

        // 显示标题和实时状态
        OLED_ShowString(0, 0, "Vision Debug", 12, 0);
        Oled_Printf(0, 1, 12, 0, "F:%d T:%ds", frame_counter % 1000, current_time/1000);

        // 获取数据统计信息
        uint32_t total_packets, valid_packets, last_time;
        GetVisionDataStats(&total_packets, &valid_packets, &last_time);

        // 检查数据是否实时
        uint8_t is_realtime = IsVisionDataRealtime();

        // 显示数据统计和状态
        Oled_Printf(0, 2, 12, 0, "Rx:%d Ok:%d", total_packets % 1000, valid_packets % 1000);
        Oled_Printf(80, 2, 12, 0, is_realtime ? "LIVE" : "OLD");

        // 获取最新视觉数据
        RectangleData_t vision_data;
        if(GetLatestVisionData(&vision_data)) {
            // 显示检测到的目标数量
            Oled_Printf(0, 3, 12, 0, "Count: %d", vision_data.count);

            // 显示前3个目标的坐标（优化显示空间）
            uint8_t display_count = (vision_data.count > 3) ? 3 : vision_data.count;
            uint8_t i;
            for(i = 0; i < display_count; i++) {
                Oled_Printf(0, 4 + i, 12, 0, "%d:(%d,%d)%.1f",
                           i+1,
                           vision_data.rects[i].x,
                           vision_data.rects[i].y,
                           vision_data.rects[i].exist_time);
            }
        } else {
            // 没有新数据时显示详细状态
            if(is_realtime) {
                OLED_ShowString(0, 4, "Processing...", 12, 0);
            } else {
                uint32_t data_age = (current_time - last_time) / 1000;
                Oled_Printf(0, 4, 12, 0, "No data %ds", data_age);
                OLED_ShowString(0, 5, "Check USART6", 12, 0);

                // 每3秒注入一次测试数据验证解析功能
                if(frame_counter % 60 == 0) {
                    InjectTestVisionData();
                    OLED_ShowString(0, 6, "INJECT TEST", 12, 0);
                }

                // 显示调试信息
                Oled_Printf(0, 7, 12, 0, "Debug Mode");
            }
        }
    }
}

// OLED处理函数，用于任务调度
void oled_proc(void)
{
    Oled_Task();
}
