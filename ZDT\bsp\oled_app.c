#include "oled_app.h"

//extern Encoder left_encoder;
//extern Encoder right_encoder;

//extern MOTOR left_motor;
//extern MOTOR right_motor;

//extern PID_T pid_speed_left;  // �����ٶȻ� PID ������
//extern PID_T pid_speed_right; // �����ٶȻ� PID ������

//extern PID_T pid_angle; // ǰ���ǶȻ�

//extern PID_T pid_line; // ѭ��λ��ʽ PID ������(ѭ����)

extern float pitch, roll, yaw; // pitch-�����ǣ���-��+����roll����ǣ�ǰ-��+����yaw-����ǣ���+��-����Ҫ���������ǵ�xyz�ῴ

extern unsigned char Digtal; // �Ҷȴ�����������
extern float g_line_position_error; // ѭ�����ֵ��ѭ������ǰֵ��

extern unsigned char system_mode; // ϵͳģʽ��1~4 ��Ӧ�ĵ���Ŀ

extern unsigned char gray_ff_count; // ��Ȧ����Ȧ������

void Oled_Init(void)
{
  OLED_Init();
  OLED_Clear();
}

void Oled_Task(void)
{
    static uint32_t last_update = 0;
    uint32_t current_time = HAL_GetTick();

    // 每100ms更新一次显示
    if(current_time - last_update >= 100) {
        last_update = current_time;

        // 清屏
//        OLED_Clear();

        // 显示标题
        OLED_ShowString(0, 0, "Vision Data:", 12, 0);

        // 获取最新视觉数据
        RectangleData_t vision_data;
        if(GetLatestVisionData(&vision_data)) {
            // 显示检测到的目标数量
            Oled_Printf(0, 2, 12, 0, "Count: %d", vision_data.count);

            // 显示前3个目标的坐标（OLED屏幕空间有限）
            uint8_t display_count = (vision_data.count > 3) ? 3 : vision_data.count;
            uint8_t i;
            for(i = 0; i < display_count; i++) {
                Oled_Printf(0, 4 + i*2, 12, 0, "%d:(%d,%d)%.1fs",
                           i+1,
                           vision_data.rects[i].x,
                           vision_data.rects[i].y,
                           vision_data.rects[i].exist_time);
            }
        } else {
            // 没有新数据时显示等待信息
            OLED_ShowString(0, 2, "Waiting...", 12, 0);
        }
    }
}

// OLED处理函数，用于任务调度
void oled_proc(void)
{
    Oled_Task();
}
