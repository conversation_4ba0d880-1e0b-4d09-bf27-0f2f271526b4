# STM32F407 双轴步进电机控制系统

## 项目概述
基于STM32F407的双轴步进电机控制系统，支持X/Y轴独立控制，集成树莓派视觉反馈和PID控制算法。

## 硬件配置

### 串口分配
- **USART1**: PA9(TX), PA10(RX) - 调试串口 (115200)
- **USART2**: PA2(TX), PA3(RX) - X轴步进电机通信 (115200, DMA)
- **USART3**: PB10(TX), PB11(RX) - Y轴步进电机通信 (115200, DMA) 
- **UART4**: PA0(TX), PA1(RX) - 已禁用
- **USART6**: PC6(TX), PC7(RX) - 树莓派通信 (115200, DMA)

### DMA配置
- **DMA1_Stream5**: USART2_RX (X轴电机反馈)
- **DMA1_Stream1**: USART3_RX (Y轴电机反馈)
- **DMA2_Stream1**: USART6_RX (树莓派数据)

### I2C配置
- **I2C3**: PC9(SDA), PA8(SCL) - OLED显示屏通信 (100kHz)
  - 设备地址: 0x78 (SSD1306 OLED)
  - 功能: 实时显示视觉坐标数据

## 最近更新 (2025-08-02)

### 问题解决：OLED显示视觉坐标数据不显示
**问题描述**: OLED显示屏能正常工作，但通过串口解析的视觉坐标数据无法显示

**根本原因分析**:
1. **USART6 DMA接收未启动**: 树莓派发送的视觉数据通过USART6接收，但DMA接收在初始化时未启动
2. **Oled_Printf参数不匹配**: 调用时使用4个参数，但函数定义需要5个参数
3. **OLED清屏被注释**: 可能导致显示内容重叠

**解决方案**:
1. **启动USART6 DMA接收**: 在main.c初始化中添加`HAL_UARTEx_ReceiveToIdle_DMA(&huart6, pi_rx_buf, sizeof(pi_rx_buf))`
2. **修复Oled_Printf调用**: 确保参数匹配，使用正确的字体大小和颜色参数
3. **启用OLED清屏**: 取消注释`OLED_Clear()`以避免显示重叠
4. **添加测试数据**: 在没有真实数据时生成测试数据验证显示功能
5. **变量作用域修复**: 将pi_bsp.c中的静态变量改为全局变量，供oled_app.c访问

### 问题解决：OLED显示屏不显示 (2025-01-29)
**问题描述**: OLED在while循环中调用显示函数但无显示输出

**根本原因**: I2C2引脚与USART3引脚冲突
- 原配置：I2C2使用PB10(SCL)/PB11(SDA)
- 冲突：USART3也使用PB10(TX)/PB11(RX)用于Y轴电机通信

**解决方案**: 重新配置I2C2引脚
- 新配置：I2C2使用PF0(SDA)/PF1(SCL)
- 避免了与USART3的引脚冲突
- 更新了相关的GPIO初始化和反初始化代码

### 问题解决：Y轴电机不动
**问题描述**: 原本使用UART4控制Y轴电机时，电机无响应

**解决方案**: 将Y轴电机控制从UART4切换到USART3，采用中断处理框架
- 修改了 `bsp/step_motor_bsp.h` 中的宏定义
- 更新了中断回调函数处理
- 采用USART3_IRQHandler中断处理框架管理DMA接收
- 禁用了UART4相关功能

### 修改文件列表 (2025-08-02)
1. **bsp/oled_app.c**
   - 取消注释`OLED_Clear()`以避免显示重叠
   - 修复Oled_Printf参数匹配问题
   - 添加测试数据生成功能
   - 添加外部变量声明以访问视觉数据

2. **bsp/pi_bsp.c**
   - 将静态变量`latest_vision_data`和`vision_data_updated`改为全局变量

3. **Core/Src/main.c**
   - 添加USART6 DMA接收启动：`HAL_UARTEx_ReceiveToIdle_DMA(&huart6, pi_rx_buf, sizeof(pi_rx_buf))`
   - 添加外部变量声明：motor_x_buf, motor_y_buf, pi_rx_buf

4. **README.md**
   - 更新问题解决记录和技术实现说明

### 修改文件列表 (2025-01-29)
1. **bsp/step_motor_bsp.h**
   - `MOTOR_Y_UART` 从 `huart4` 改为 `huart3`

2. **Core/Src/usart.c**
   - 中断回调函数中将 `UART4` 改为 `USART3`

3. **Core/Src/main.c**
   - 注释掉 `MX_UART4_Init()`
   - 电机控制调用从 `&huart4` 改为 `&huart3`

4. **Core/Src/stm32f4xx_it.c**
   - 将UART4_IRQHandler的DMA启动代码移植到USART3_IRQHandler
   - 注释掉UART4相关中断处理函数
   - 注释掉UART4和hdma_uart4_rx外部声明

5. **Core/Src/dma.c**
   - 注释掉DMA1_Stream2中断配置（原UART4使用）

### 技术架构：中断处理框架
```c
// USART3中断处理函数框架
void USART3_IRQHandler(void)
{
  HAL_UART_IRQHandler(&huart3);
  // 重新启动DMA接收
  HAL_UARTEx_ReceiveToIdle_DMA(&huart3, motor_y_buf, sizeof(motor_y_buf));
  // 禁用DMA半传输中断
  __HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
}
```

### 数据流程
1. **发送命令**: 通过USART3发送Emm_V5协议命令到Y轴电机
2. **接收反馈**: 电机反馈数据触发USART3中断
3. **DMA处理**: 中断处理函数自动重启DMA接收
4. **数据解析**: HAL_UARTEx_RxEventCallback处理接收到的数据

## 系统架构

### 电机控制
- 支持位置控制和速度控制
- PID闭环控制算法
- 实时位置反馈

### 视觉反馈
- 树莓派提供激光坐标数据
- 数据格式: `red:(x,y)` 和 `gre:(x,y)`
- 实时位置校正

### 通信协议
- Emm_V5步进电机控制协议
- DMA + 环形缓冲区数据处理
- 中断驱动的实时响应

## 使用说明

### 编译环境
- Keil MDK-ARM
- STM32CubeMX配置
- HAL库驱动

### 初始化流程
1. 系统时钟配置
2. GPIO和DMA初始化  
3. 串口初始化
4. 环形缓冲区初始化
5. 步进电机初始化
6. 中断处理函数自动管理DMA接收

### 电机控制API
```c
// 电机使能控制
Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, false);

// 位置控制
Emm_V5_Pos_Control(&huart3, 1, 0, 100, 0, 800, 0, 0);

// 速度控制
Step_Motor_Set_Speed_my(x_rpm, y_rpm);
```

### 实时视觉数据API
```c
// 获取最新视觉数据
RectangleData_t vision_data;
if(GetLatestVisionData(&vision_data)) {
    // 处理视觉数据
    printf("检测到 %d 个目标\n", vision_data.count);
}

// 检查数据实时性
if(IsVisionDataRealtime()) {
    printf("数据实时更新中\n");
} else {
    printf("数据已过期\n");
}

// 获取性能统计
uint32_t total, valid, last_time;
GetVisionDataStats(&total, &valid, &last_time);
printf("总包数:%d 有效包数:%d\n", total, valid);
```

## 故障排除

### Y轴电机不动
- ✅ 已解决：切换到USART3并采用中断处理框架
- 检查DMA配置是否正确
- 确认GPIO引脚配置
- 验证电机连接和供电

### 通信异常
- 检查波特率设置
- 验证中断处理函数是否正常工作
- 查看环形缓冲区数据

## OLED视觉数据显示功能

### 功能描述
系统现在支持在OLED屏幕上实时显示从树莓派视觉系统接收的目标坐标数据。

### 显示内容
- **标题**: "Vision Data:"
- **目标数量**: 检测到的矩形目标数量
- **坐标信息**: 每个目标的坐标(x,y)和存在时间
- **显示格式**:
  ```
  Vision Data:
  Count: 2
  1:(150,100)1.5s
  2:(200,150)0.8s
  3:(300,200)2.1s
  ```

### 实时技术实现
- **数据源**: USART6接收树莓派连续发送的视觉数据
- **数据格式**: "RECT,count,x1,y1,t1,x2,y2,t2,..." (支持换行符分隔)
- **高速接收**: DMA + 128字节接收缓冲区 + 256字节环形缓冲区
- **实时解析**: `ParseRectangleData()` + 512字节行缓冲区处理不完整数据
- **高频调度**: pi_proc()每5ms执行，oled_proc()每50ms执行
- **数据监控**: 实时统计接收包数、有效包数、数据新鲜度
- **状态指示**: LIVE/OLD状态显示，数据延迟监控
- **性能优化**: while循环处理所有可用数据，避免数据积压

### 实时数据流程 (优化后)
1. **树莓派连续发送**: 视觉处理结果实时通过USART6发送到STM32
2. **DMA高速接收**: HAL_UARTEx_ReceiveToIdle_DMA自动接收数据到pi_rx_buf(128字节)
3. **中断快速处理**: USART6_IRQHandler触发，数据立即存入环形缓冲区ringbuffer_pi(256字节)
4. **实时数据解析**: pi_proc()每5ms执行，持续读取并解析所有可用数据
5. **行缓冲处理**: 使用512字节行缓冲区处理不完整数据包，确保数据完整性
6. **实时显示更新**: Oled_Task()每50ms更新显示，包含数据统计和实时状态
7. **数据监控**: 实时监控接收包数、有效包数和数据新鲜度

### 相关文件
- `bsp/pi_bsp.c` - 视觉数据解析和存储
- `bsp/oled_app.c` - OLED显示逻辑
- `bsp/schedule.c` - 任务调度管理
- `0.96 Oled/oled.c` - OLED底层驱动
- `app/oled_driver.c` - OLED格式化输出函数

## 开发团队
- 硬件设计：STM32F407开发板
- 软件开发：HAL库 + 自定义BSP层
- 算法实现：PID控制 + 视觉反馈
