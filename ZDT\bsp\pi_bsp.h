#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// 矩形信息结构体
typedef struct {
    int x;              // 矩形中心x坐标
    int y;              // 矩形中心y坐标
    float exist_time;   // 存在时间(秒)
} Rectangle_t;

// 矩形数据存储结构体
typedef struct {
    uint8_t count;                  // 检测到的矩形数量
    Rectangle_t rects[10];          // 矩形数组
} RectangleData_t;

void pi_proc(void); // 树莓派处理函数
uint8_t ParseRectangleData(char* buffer, uint16_t length, RectangleData_t* rect_data);
uint8_t GetLatestVisionData(RectangleData_t* data); // 获取最新视觉数据

// 实时数据监控函数
void GetVisionDataStats(uint32_t* total_packets, uint32_t* valid_packets, uint32_t* last_time);
uint8_t IsVisionDataRealtime(void); // 检查数据是否实时

#endif
